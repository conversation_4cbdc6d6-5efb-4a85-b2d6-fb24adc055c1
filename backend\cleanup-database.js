const mongoose = require('mongoose');
require('dotenv').config();

const Poll = require('./models/Poll');
const Quiz = require('./models/Quiz');

async function cleanupDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);

    console.log('🔗 Connected to MongoDB');

    // Delete all polls
    const deletedPolls = await Poll.deleteMany({});
    console.log(`🗑️ Deleted ${deletedPolls.deletedCount} polls`);

    // Delete all quizzes
    const deletedQuizzes = await Quiz.deleteMany({});
    console.log(`🗑️ Deleted ${deletedQuizzes.deletedCount} quizzes`);

    console.log('✅ Database cleanup completed!');
    console.log('📝 You now have a clean database with no polls or quizzes');

    // Close connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error cleaning database:', error);
    process.exit(1);
  }
}

// Run cleanup
cleanupDatabase();
