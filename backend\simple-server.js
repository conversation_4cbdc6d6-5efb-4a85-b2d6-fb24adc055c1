const http = require('http');
const url = require('url');
const querystring = require('querystring');

// In-memory storage
let polls = [];
let quizzes = [];
let pollCounter = 1;
let quizCounter = 1;

// Generate session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// CORS headers
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}

// Parse JSON body
function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = body ? JSON.parse(body) : {};
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

const server = http.createServer((req, res) => {
  setCORSHeaders(res);
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path} - ${new Date().toISOString()}`);

  // Health check
  if (path === '/api/health' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Live Polling and Quiz API is running',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Create poll
  if (path === '/api/polls' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendJSON(res, 400, { error: 'Invalid JSON' });
        return;
      }

      console.log('Creating poll with data:', body);
      const { question, options } = body;
      
      if (!question || !options || options.length < 2 || options.length > 5) {
        console.log('Validation failed:', { question, options });
        sendJSON(res, 400, { error: 'Question is required and must have 2-5 options' });
        return;
      }

      const formattedOptions = options.map(option => ({
        text: option,
        voteCount: 0
      }));

      const sessionCode = generateSessionCode();
      const poll = {
        _id: `poll_${pollCounter++}`,
        sessionCode: sessionCode,
        question,
        options: formattedOptions,
        isActive: true,
        showResults: true,
        totalVotes: 0,
        participants: [],
        createdAt: new Date()
      };

      polls.push(poll);
      
      console.log('Poll created successfully:', poll);
      sendJSON(res, 201, {
        ...poll,
        joinUrl: `http://localhost:3000/poll/${poll._id}`
      });
    });
    return;
  }

  // Get polls
  if (path === '/api/polls' && method === 'GET') {
    console.log('Getting all polls');
    sendJSON(res, 200, polls);
    return;
  }

  // Create quiz
  if (path === '/api/quizzes' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendJSON(res, 400, { error: 'Invalid JSON' });
        return;
      }

      console.log('Creating quiz with data:', body);
      const { question, options, correctAnswer } = body;
      
      if (!question || !options || options.length < 2 || options.length > 5) {
        console.log('Quiz validation failed:', { question, options });
        sendJSON(res, 400, { error: 'Question is required and must have 2-5 options' });
        return;
      }

      if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
        console.log('Quiz correct answer validation failed:', { correctAnswer, optionsLength: options.length });
        sendJSON(res, 400, { error: 'Valid correct answer index is required' });
        return;
      }

      const formattedOptions = options.map(option => ({
        text: option,
        selectedCount: 0
      }));

      const sessionCode = generateSessionCode();
      const quiz = {
        _id: `quiz_${quizCounter++}`,
        sessionCode: sessionCode,
        question,
        options: formattedOptions,
        correctAnswer,
        isActive: true,
        showResults: true,
        totalAnswers: 0,
        participants: [],
        createdAt: new Date()
      };

      quizzes.push(quiz);
      
      console.log('Quiz created successfully:', quiz);
      sendJSON(res, 201, {
        ...quiz,
        joinUrl: `http://localhost:3000/quiz/${quiz._id}`
      });
    });
    return;
  }

  // Get quizzes
  if (path === '/api/quizzes' && method === 'GET') {
    console.log('Getting all quizzes');
    sendJSON(res, 200, quizzes);
    return;
  }

  // Session lookup
  if (path.startsWith('/api/sessions/') && method === 'GET') {
    const code = path.split('/')[3].toUpperCase();
    console.log('Looking up session code:', code);

    // Check polls first
    const poll = polls.find(p => p.sessionCode === code);
    if (poll) {
      console.log('Found poll for code:', code);
      sendJSON(res, 200, { ...poll, type: 'poll' });
      return;
    }

    // Check quizzes
    const quiz = quizzes.find(q => q.sessionCode === code);
    if (quiz) {
      console.log('Found quiz for code:', code);
      sendJSON(res, 200, { ...quiz, type: 'quiz' });
      return;
    }

    console.log('Session not found for code:', code);
    sendJSON(res, 404, { error: 'Session not found' });
    return;
  }

  // 404 for all other routes
  sendJSON(res, 404, { error: 'Route not found' });
});

const PORT = 5000;

server.listen(PORT, () => {
  console.log(`Live Polling and Quiz Server running on port ${PORT}`);
  console.log('Using in-memory storage (no external dependencies)');
  console.log('CORS enabled for all origins');
});

module.exports = server;
