{"ast": null, "code": "import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup\ntestBackend();\n\n// Enhanced API wrapper with fallback to mock\nconst enhancedApi = {\n  async get(url) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.getPolls();\n      if (url === '/quizzes') return mockApi.getQuizzes();\n      if (url.startsWith('/polls/')) {\n        const id = url.split('/')[2];\n        return mockApi.getPoll(id);\n      }\n      if (url.startsWith('/quizzes/')) {\n        const id = url.split('/')[2];\n        return mockApi.getQuiz(id);\n      }\n      if (url.startsWith('/sessions/')) {\n        const code = url.split('/')[2];\n        return mockApi.getSession(code);\n      }\n    }\n    try {\n      const response = await api.get(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.get(url); // Retry with mock\n    }\n  },\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n  }\n};\nexport default enhancedApi;", "map": {"version": 3, "names": ["axios", "mockApi", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "useBackend", "testBackend", "get", "console", "log", "error", "enhancedApi", "url", "getPolls", "getQuizzes", "startsWith", "id", "split", "getPoll", "getQuiz", "code", "getSession", "response", "status", "message", "post", "data", "createPoll", "createQuiz", "includes", "pollId", "vote", "quizId", "answer", "patch", "success"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production'\n    ? '/api'\n    : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup\ntestBackend();\n\n// Enhanced API wrapper with fallback to mock\nconst enhancedApi = {\n  async get(url) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.getPolls();\n      if (url === '/quizzes') return mockApi.getQuizzes();\n      if (url.startsWith('/polls/')) {\n        const id = url.split('/')[2];\n        return mockApi.getPoll(id);\n      }\n      if (url.startsWith('/quizzes/')) {\n        const id = url.split('/')[2];\n        return mockApi.getQuiz(id);\n      }\n      if (url.startsWith('/sessions/')) {\n        const code = url.split('/')[2];\n        return mockApi.getSession(code);\n      }\n    }\n\n    try {\n      const response = await api.get(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.get(url); // Retry with mock\n    }\n  },\n\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return { data: { success: true } };\n    }\n\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return { data: { success: true } };\n    }\n  }\n};\n\nexport default enhancedApi;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1C,MAAM,GACN,2BAA2B;EAC/BC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,UAAU,GAAG,IAAI;;AAErB;AACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EAC9B,IAAI;IACF,MAAMT,GAAG,CAACU,GAAG,CAAC,SAAS,CAAC;IACxBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnCJ,UAAU,GAAG,IAAI;EACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpDJ,UAAU,GAAG,KAAK;EACpB;AACF,CAAC;;AAED;AACAC,WAAW,CAAC,CAAC;;AAEb;AACA,MAAMK,WAAW,GAAG;EAClB,MAAMJ,GAAGA,CAACK,GAAG,EAAE;IACb,IAAI,CAACP,UAAU,EAAE;MACf;MACA,IAAIO,GAAG,KAAK,QAAQ,EAAE,OAAOhB,OAAO,CAACiB,QAAQ,CAAC,CAAC;MAC/C,IAAID,GAAG,KAAK,UAAU,EAAE,OAAOhB,OAAO,CAACkB,UAAU,CAAC,CAAC;MACnD,IAAIF,GAAG,CAACG,UAAU,CAAC,SAAS,CAAC,EAAE;QAC7B,MAAMC,EAAE,GAAGJ,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAOrB,OAAO,CAACsB,OAAO,CAACF,EAAE,CAAC;MAC5B;MACA,IAAIJ,GAAG,CAACG,UAAU,CAAC,WAAW,CAAC,EAAE;QAC/B,MAAMC,EAAE,GAAGJ,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAOrB,OAAO,CAACuB,OAAO,CAACH,EAAE,CAAC;MAC5B;MACA,IAAIJ,GAAG,CAACG,UAAU,CAAC,YAAY,CAAC,EAAE;QAChC,MAAMK,IAAI,GAAGR,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9B,OAAOrB,OAAO,CAACyB,UAAU,CAACD,IAAI,CAAC;MACjC;IACF;IAEA,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMzB,GAAG,CAACU,GAAG,CAACK,GAAG,CAAC;MACnCJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEa,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACc,OAAO,EAAE,0BAA0B,CAAC;MACtEnB,UAAU,GAAG,KAAK;MAClB,OAAO,IAAI,CAACE,GAAG,CAACK,GAAG,CAAC,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMa,IAAIA,CAACb,GAAG,EAAEc,IAAI,EAAE;IACpB,IAAI,CAACrB,UAAU,EAAE;MACf;MACA,IAAIO,GAAG,KAAK,QAAQ,EAAE,OAAOhB,OAAO,CAAC+B,UAAU,CAACD,IAAI,CAAC;MACrD,IAAId,GAAG,KAAK,UAAU,EAAE,OAAOhB,OAAO,CAACgC,UAAU,CAACF,IAAI,CAAC;MACvD,IAAId,GAAG,CAACiB,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzB,MAAMC,MAAM,GAAGlB,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAOrB,OAAO,CAACmC,IAAI,CAACD,MAAM,EAAEJ,IAAI,CAAC;MACnC;MACA,IAAId,GAAG,CAACiB,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC3B,MAAMG,MAAM,GAAGpB,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAOrB,OAAO,CAACqC,MAAM,CAACD,MAAM,EAAEN,IAAI,CAAC;MACrC;IACF;IAEA,IAAI;MACFlB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAEG,GAAG,EAAEc,IAAI,CAAC;MAC9C,MAAMJ,QAAQ,GAAG,MAAMzB,GAAG,CAAC4B,IAAI,CAACb,GAAG,EAAEc,IAAI,CAAC;MAC1ClB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEa,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACc,OAAO,EAAE,0BAA0B,CAAC;MACtEnB,UAAU,GAAG,KAAK;MAClB,OAAO,IAAI,CAACoB,IAAI,CAACb,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMQ,KAAKA,CAACtB,GAAG,EAAEc,IAAI,EAAE;IACrB,IAAI,CAACrB,UAAU,EAAE;MACfG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEG,GAAG,CAAC;MACvD,OAAO;QAAEc,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;IAEA,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMzB,GAAG,CAACqC,KAAK,CAACtB,GAAG,EAAEc,IAAI,CAAC;MAC3ClB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEa,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACc,OAAO,CAAC;MAC1CnB,UAAU,GAAG,KAAK;MAClB,OAAO;QAAEqB,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;EACF;AACF,CAAC;AAED,eAAexB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}