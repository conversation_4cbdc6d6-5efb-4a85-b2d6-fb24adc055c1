import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import api from '../api';
import socket from '../socket';

function PollSession() {
  const { id } = useParams();
  const [poll, setPoll] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [hasVoted, setHasVoted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchPoll = useCallback(async () => {
    try {
      const response = await api.get(`/polls/${id}`);
      setPoll(response.data);
      
      // Check if user already voted
      const participant = response.data.participants?.find(p => p.socketId === socket.id);
      if (participant && participant.hasVoted) {
        setHasVoted(true);
        setSelectedOption(participant.selectedOption);
      }
    } catch (err) {
      setError('Poll not found or inactive');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchPoll();

    // Join poll room
    socket.emit('join-poll', id);

    // Listen for real-time updates
    socket.on('poll-update', (data) => {
      console.log('📊 Poll update received:', data);
      setPoll(data.poll);
    });

    socket.on('results-visibility-changed', (data) => {
      console.log('👁️ Results visibility changed:', data);
      setPoll(data.poll);
    });

    socket.on('vote-success', (data) => {
      console.log('✅ Vote success:', data);
      setHasVoted(true);
    });

    socket.on('error', (data) => {
      console.error('❌ Socket error:', data);
      setError(data.message);
    });

    return () => {
      socket.off('poll-update');
      socket.off('results-visibility-changed');
      socket.off('vote-success');
      socket.off('error');
    };
  }, [id, fetchPoll]);

  const handleVote = (optionIndex) => {
    console.log('🗳️ Vote attempt:', { optionIndex, hasVoted, isActive: poll.isActive, socketId: socket.id });

    if (hasVoted || !poll.isActive) {
      console.log('❌ Vote blocked:', { hasVoted, isActive: poll.isActive });
      return;
    }

    setSelectedOption(optionIndex);

    // Emit vote via socket
    console.log('📡 Emitting vote:', { pollId: id, optionIndex, socketId: socket.id });
    socket.emit('vote', {
      pollId: id,
      optionIndex,
      socketId: socket.id
    });
  };

  const getTotalVotes = () => {
    if (!poll) return 0;
    return poll.options.reduce((total, option) => total + option.voteCount, 0);
  };

  const getPercentage = (voteCount) => {
    const total = getTotalVotes();
    return total === 0 ? 0 : Math.round((voteCount / total) * 100);
  };

  if (loading) {
    return <div className="loading">Loading poll...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!poll) {
    return <div className="error">Poll not found</div>;
  }

  return (
    <div className="session-container">
      <div className="session-header">
        <div className="session-type">📊 Live Poll</div>
        <div className={`session-status ${poll.isActive ? 'active' : 'ended'}`}>
          {poll.isActive ? 'Active' : 'Ended'}
        </div>
      </div>

      <div className="question-card">
        <h2 className="question-text">{poll.question}</h2>
        
        {!hasVoted && poll.isActive ? (
          <div className="options-container">
            <p className="instruction">Choose your answer:</p>
            <ul className="options-list">
              {poll.options.map((option, index) => (
                <li 
                  key={index} 
                  className="option-item"
                  onClick={() => handleVote(index)}
                >
                  <div className="option-letter">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <div className="option-text">{option.text}</div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="feedback-container">
            {hasVoted ? (
              <div className="feedback success">
                <div className="feedback-icon">✅</div>
                <div className="feedback-text">
                  Thank you for voting! Your response has been recorded.
                </div>
              </div>
            ) : (
              <div className="feedback info">
                <div className="feedback-icon">⏰</div>
                <div className="feedback-text">
                  This poll has ended. No more votes are being accepted.
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Show results based on poll settings */}
      {poll.showResults && (
        <div className="results-card">
          <h3 className="results-title">
            📊 Live Results ({getTotalVotes()} total votes)
          </h3>

          <div className="results-list">
            {poll.options.map((option, index) => (
              <div key={index} className="result-item">
                <div className="result-header">
                  <div className="result-option">
                    <div className="option-letter">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <span className="option-text">{option.text}</span>
                  </div>
                  <span className="result-count">{option.voteCount} votes</span>
                </div>
                <div className="result-bar">
                  <div
                    className={`result-fill ${selectedOption === index ? 'user-selected' : ''}`}
                    style={{ width: `${getPercentage(option.voteCount)}%` }}
                  >
                    {getPercentage(option.voteCount)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!poll.showResults && (
        <div className="results-hidden">
          <h3>📊 Results Hidden</h3>
          <p>The poll creator has hidden the results. They will be visible when the creator chooses to show them.</p>
        </div>
      )}

      <div className="session-info">
        <p>Session ID: {poll._id}</p>
        <p>Participants: {poll.participants?.length || 0}</p>
      </div>
    </div>
  );
}

export default PollSession;
