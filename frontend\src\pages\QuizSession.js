import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import api from '../api';
import socket from '../socket';

function QuizSession() {
  const { id } = useParams();
  const [quiz, setQuiz] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchQuiz = useCallback(async () => {
    try {
      console.log('🔍 Fetching quiz with ID:', id);
      const response = await api.get(`/quizzes/${id}`);
      console.log('✅ Quiz fetched successfully:', response.data);
      setQuiz(response.data);

      // Check if user already answered
      const participant = response.data.participants?.find(p => p.socketId === socket.id);
      if (participant && participant.hasAnswered) {
        setHasAnswered(true);
        setSelectedOption(participant.selectedOption);
        setFeedback({
          isCorrect: participant.isCorrect,
          correctAnswer: response.data.correctAnswer,
          selectedAnswer: participant.selectedOption
        });
      }
    } catch (err) {
      console.error('❌ Quiz fetch error:', err);
      if (err.response?.status === 404) {
        setError('Quiz not found. Please check the URL or contact the quiz creator.');
      } else if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError('Failed to load quiz. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchQuiz();

    // Join quiz room
    socket.emit('join-quiz', id);

    // Listen for real-time updates
    socket.on('quiz-update', (data) => {
      setQuiz(data.quiz);
    });

    socket.on('results-visibility-changed', (data) => {
      setQuiz(data.quiz);
    });

    socket.on('answer-feedback', (data) => {
      setFeedback(data);
      setHasAnswered(true);
    });

    socket.on('error', (data) => {
      setError(data.message);
    });

    return () => {
      socket.off('quiz-update');
      socket.off('results-visibility-changed');
      socket.off('answer-feedback');
      socket.off('error');
    };
  }, [id, fetchQuiz]);

  const handleAnswer = (optionIndex) => {
    if (hasAnswered || !quiz.isActive) return;
    
    setSelectedOption(optionIndex);
    
    // Emit answer via socket
    socket.emit('answer', {
      quizId: id,
      optionIndex,
      socketId: socket.id
    });
  };

  const getTotalAnswers = () => {
    if (!quiz) return 0;
    return quiz.options.reduce((total, option) => total + option.selectedCount, 0);
  };

  const getPercentage = (selectedCount) => {
    const total = getTotalAnswers();
    return total === 0 ? 0 : Math.round((selectedCount / total) * 100);
  };

  const getCorrectAnswers = () => {
    if (!quiz) return 0;
    return quiz.options[quiz.correctAnswer]?.selectedCount || 0;
  };

  const getSuccessRate = () => {
    const total = getTotalAnswers();
    const correct = getCorrectAnswers();
    return total > 0 ? Math.round((correct / total) * 100) : 0;
  };

  if (loading) {
    return <div className="loading">Loading quiz...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!quiz) {
    return <div className="error">Quiz not found</div>;
  }

  return (
    <div className="session-container">
      <div className="session-header">
        <div className="session-type">🧠 Live Quiz</div>
        <div className={`session-status ${quiz.isActive ? 'active' : 'ended'}`}>
          {quiz.isActive ? 'Active' : 'Ended'}
        </div>
      </div>

      <div className="question-card">
        <h2 className="question-text">{quiz.question}</h2>
        
        {!hasAnswered && quiz.isActive ? (
          <div className="options-container">
            <p className="instruction">Choose your answer:</p>
            <ul className="options-list">
              {quiz.options.map((option, index) => (
                <li 
                  key={index} 
                  className="option-item"
                  onClick={() => handleAnswer(index)}
                >
                  <div className="option-letter">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <div className="option-text">{option.text}</div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="feedback-container">
            {hasAnswered && feedback ? (
              <div className={`feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`}>
                <div className="feedback-icon">
                  {feedback.isCorrect ? '🎉' : '❌'}
                </div>
                <div className="feedback-text">
                  {feedback.isCorrect ? (
                    <div>
                      <strong>Correct!</strong>
                      <p>Great job! You got it right.</p>
                    </div>
                  ) : (
                    <div>
                      <strong>Incorrect.</strong>
                      <p>The correct answer was: <strong>{quiz.options[feedback.correctAnswer]?.text}</strong></p>
                    </div>
                  )}
                </div>
              </div>
            ) : !quiz.isActive ? (
              <div className="feedback info">
                <div className="feedback-icon">⏰</div>
                <div className="feedback-text">
                  This quiz has ended. No more answers are being accepted.
                </div>
              </div>
            ) : null}
          </div>
        )}
      </div>

      {/* Show results based on quiz settings */}
      {quiz.showResults && (
        <div className="results-card">
          <h3 className="results-title">
            📊 Live Results ({getTotalAnswers()} total answers)
          </h3>

          <div className="quiz-stats">
            <div className="stat-item">
              <span className="stat-value">{getCorrectAnswers()}</span>
              <span className="stat-label">Correct Answers</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{getTotalAnswers() - getCorrectAnswers()}</span>
              <span className="stat-label">Incorrect Answers</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{getSuccessRate()}%</span>
              <span className="stat-label">Success Rate</span>
            </div>
          </div>

          <div className="results-list">
            {quiz.options.map((option, index) => (
              <div key={index} className="result-item">
                <div className="result-header">
                  <div className="result-option">
                    <div className="option-letter">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <span className="option-text">
                      {option.text}
                      {index === quiz.correctAnswer && ' ✅'}
                    </span>
                  </div>
                  <span className="result-count">{option.selectedCount} answers</span>
                </div>
                <div className="result-bar">
                  <div
                    className={`result-fill ${
                      index === quiz.correctAnswer ? 'correct' : ''
                    } ${selectedOption === index ? 'user-selected' : ''}`}
                    style={{ width: `${getPercentage(option.selectedCount)}%` }}
                  >
                    {getPercentage(option.selectedCount)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!quiz.showResults && (
        <div className="results-hidden">
          <h3>📊 Results Hidden</h3>
          <p>The quiz creator has hidden the results. They will be visible when the creator chooses to show them.</p>
        </div>
      )}

      <div className="session-info">
        <p>Session ID: {quiz._id}</p>
        <p>Participants: {quiz.participants?.length || 0}</p>
      </div>
    </div>
  );
}

export default QuizSession;
