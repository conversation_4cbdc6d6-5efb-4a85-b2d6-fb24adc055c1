{"ast": null, "code": "import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper - BACKEND ONLY (no mock fallback)\nconst enhancedApi = {\n  async get(url) {\n    try {\n      const response = await api.get(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n  },\n  async delete(url) {\n    if (!useBackend) {\n      console.log('Mock API: DELETE not implemented for', url);\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n    try {\n      const response = await api.delete(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n  }\n};\nexport default enhancedApi;", "map": {"version": 3, "names": ["axios", "mockApi", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "useBackend", "testBackend", "get", "console", "log", "error", "initializeApi", "setTimeout", "window", "apiInitialized", "enhancedApi", "url", "response", "status", "message", "post", "data", "createPoll", "createQuiz", "includes", "pollId", "split", "vote", "quizId", "answer", "patch", "success", "delete"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production'\n    ? '/api'\n    : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper - BACKEND ONLY (no mock fallback)\nconst enhancedApi = {\n  async get(url) {\n    try {\n      const response = await api.get(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return { data: { success: true } };\n    }\n\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return { data: { success: true } };\n    }\n  },\n\n  async delete(url) {\n    if (!useBackend) {\n      console.log('Mock API: DELETE not implemented for', url);\n      return { data: { success: true } };\n    }\n\n    try {\n      const response = await api.delete(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return { data: { success: true } };\n    }\n  }\n};\n\nexport default enhancedApi;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1C,MAAM,GACN,2BAA2B;EAC/BC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,UAAU,GAAG,IAAI;;AAErB;AACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EAC9B,IAAI;IACF,MAAMT,GAAG,CAACU,GAAG,CAAC,SAAS,CAAC;IACxBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCJ,UAAU,GAAG,IAAI;EACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDJ,UAAU,GAAG,KAAK;EACpB;AACF,CAAC;;AAED;AACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,MAAML,WAAW,CAAC,CAAC;EACnB,IAAI,CAACD,UAAU,EAAE;IACf;IACAO,UAAU,CAAC,YAAY;MACrB,MAAMN,WAAW,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC;;AAED;AACA,IAAI,CAACO,MAAM,CAACC,cAAc,EAAE;EAC1BD,MAAM,CAACC,cAAc,GAAG,IAAI;EAC5BH,aAAa,CAAC,CAAC;AACjB;;AAEA;AACA,MAAMI,WAAW,GAAG;EAClB,MAAMR,GAAGA,CAACS,GAAG,EAAE;IACb,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,GAAG,CAACU,GAAG,CAACS,GAAG,CAAC;MACnCR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACS,OAAO,EAAE,UAAU,EAAEH,GAAG,CAAC;MAC7D,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMU,IAAIA,CAACJ,GAAG,EAAEK,IAAI,EAAE;IACpB,IAAI,CAAChB,UAAU,EAAE;MACf;MACA,IAAIW,GAAG,KAAK,QAAQ,EAAE,OAAOpB,OAAO,CAAC0B,UAAU,CAACD,IAAI,CAAC;MACrD,IAAIL,GAAG,KAAK,UAAU,EAAE,OAAOpB,OAAO,CAAC2B,UAAU,CAACF,IAAI,CAAC;MACvD,IAAIL,GAAG,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzB,MAAMC,MAAM,GAAGT,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO9B,OAAO,CAAC+B,IAAI,CAACF,MAAM,EAAEJ,IAAI,CAAC;MACnC;MACA,IAAIL,GAAG,CAACQ,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC3B,MAAMI,MAAM,GAAGZ,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO9B,OAAO,CAACiC,MAAM,CAACD,MAAM,EAAEP,IAAI,CAAC;MACrC;IACF;IAEA,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAEO,GAAG,EAAEK,IAAI,CAAC;MAC9C,MAAMJ,QAAQ,GAAG,MAAMpB,GAAG,CAACuB,IAAI,CAACJ,GAAG,EAAEK,IAAI,CAAC;MAC1Cb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MAClD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACS,OAAO,EAAE,0BAA0B,CAAC;MACtEd,UAAU,GAAG,KAAK;MAClB,OAAO,IAAI,CAACe,IAAI,CAACJ,GAAG,EAAEK,IAAI,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMS,KAAKA,CAACd,GAAG,EAAEK,IAAI,EAAE;IACrB,IAAI,CAAChB,UAAU,EAAE;MACfG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEO,GAAG,CAAC;MACvD,OAAO;QAAEK,IAAI,EAAE;UAAEU,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;IAEA,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMpB,GAAG,CAACiC,KAAK,CAACd,GAAG,EAAEK,IAAI,CAAC;MAC3Cb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MAClD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACS,OAAO,CAAC;MAC1Cd,UAAU,GAAG,KAAK;MAClB,OAAO;QAAEgB,IAAI,EAAE;UAAEU,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,MAAMA,CAAChB,GAAG,EAAE;IAChB,IAAI,CAACX,UAAU,EAAE;MACfG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEO,GAAG,CAAC;MACxD,OAAO;QAAEK,IAAI,EAAE;UAAEU,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;IAEA,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMpB,GAAG,CAACmC,MAAM,CAAChB,GAAG,CAAC;MACtCR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MAClD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACS,OAAO,CAAC;MAC1Cd,UAAU,GAAG,KAAK;MAClB,OAAO;QAAEgB,IAAI,EAAE;UAAEU,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;EACF;AACF,CAAC;AAED,eAAehB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}