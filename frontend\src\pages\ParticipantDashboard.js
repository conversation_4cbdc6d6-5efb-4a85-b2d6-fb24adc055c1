import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../api';

function ParticipantDashboard() {
  const [sessionId, setSessionId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const joinSession = async () => {
    if (!sessionId.trim()) {
      setError('Please enter a session code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Use the new session code endpoint
      const response = await api.get(`/sessions/${sessionId.trim().toUpperCase()}`);
      const session = response.data;

      if (!session.isActive) {
        setError('This session has ended');
        return;
      }

      // Navigate to the appropriate session page using the session type
      navigate(`/${session.type}/${session._id}`);

    } catch (err) {
      setError('Session code not found or invalid');
    } finally {
      setLoading(false);
    }
  };

  // Removed handleKeyPress - using onKeyDown directly in input

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1 className="dashboard-title">Join a Session</h1>
        <p className="dashboard-subtitle">
          Enter a session code to join a live poll or quiz
        </p>
      </div>

      <div className="join-section">
        <div className="join-card">
          <div className="join-icon">🎯</div>
          <h3>Enter Session Code</h3>
          <p>Get the 6-character session code from your host</p>

          <div className="join-form">
            <input
              type="text"
              className="form-input session-input"
              placeholder="Enter Session Code (e.g., ABC123)"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value.toUpperCase())}
              onKeyDown={(e) => e.key === 'Enter' && joinSession()}
              maxLength={6}
            />
            <button
              className="btn btn-primary join-btn"
              onClick={joinSession}
              disabled={loading || !sessionId.trim()}
            >
              {loading ? 'Joining...' : 'Join Session'}
            </button>
          </div>

          {error && <div className="error-message">{error}</div>}
        </div>
      </div>

      <div className="info-section">
        <div className="info-cards">
          <div className="info-card">
            <div className="info-icon">📊</div>
            <h4>Live Polls</h4>
            <p>Vote on questions and see real-time results</p>
          </div>
          <div className="info-card">
            <div className="info-icon">🧠</div>
            <h4>Interactive Quizzes</h4>
            <p>Answer questions and get immediate feedback</p>
          </div>
          <div className="info-card">
            <div className="info-icon">⚡</div>
            <h4>Real-time Updates</h4>
            <p>See live results as others participate</p>
          </div>
        </div>
      </div>

      <div className="instructions">
        <h3>How to Join</h3>
        <ol>
          <li>Get the 6-character session code from your host</li>
          <li>Enter the code in the field above</li>
          <li>Click "Join Session" to participate</li>
          <li>Vote or answer questions in real-time</li>
        </ol>
      </div>
    </div>
  );
}

export default ParticipantDashboard;
