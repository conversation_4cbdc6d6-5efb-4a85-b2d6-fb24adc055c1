const express = require('express');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001"],
    methods: ["GET", "POST", "PATCH", "DELETE"]
  }
});

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001"],
  credentials: true
}));
app.use(express.json());

// Request logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// In-memory storage for testing (replace with MongoDB later)
let polls = [];
let quizzes = [];
let pollCounter = 1;
let quizCounter = 1;

// Generate simple session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Live Polling and Quiz API is running',
    timestamp: new Date().toISOString()
  });
});

// Poll routes
app.post('/api/polls', (req, res) => {
  try {
    console.log('Creating poll with data:', req.body);
    const { question, options } = req.body;

    if (!question || !options || options.length < 2 || options.length > 5) {
      console.log('Validation failed:', { question, options });
      return res.status(400).json({
        error: 'Question is required and must have 2-5 options'
      });
    }

    const formattedOptions = options.map(option => ({
      text: option,
      voteCount: 0
    }));

    const sessionCode = generateSessionCode();
    const poll = {
      _id: `poll_${pollCounter++}`,
      sessionCode: sessionCode,
      question,
      options: formattedOptions,
      isActive: true,
      showResults: true,
      totalVotes: 0,
      participants: [],
      createdAt: new Date()
    };

    polls.push(poll);

    const joinUrl = `http://localhost:3000/poll/${poll._id}`;

    console.log('Poll created successfully:', poll);
    res.status(201).json({
      ...poll,
      joinUrl: joinUrl
    });
  } catch (error) {
    console.error('Create poll error:', error);
    res.status(500).json({ error: 'Failed to create poll' });
  }
});

app.get('/api/polls', (req, res) => {
  res.json(polls);
});

app.get('/api/polls/:id', (req, res) => {
  const poll = polls.find(p => p._id === req.params.id);
  if (!poll) {
    return res.status(404).json({ error: 'Poll not found' });
  }
  res.json(poll);
});


// End poll session
app.patch('/api/polls/:id/end', (req, res) => {
  try {
    const poll = polls.find(p => p._id === req.params.id);
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    poll.isActive = false;
    console.log(`Poll ${req.params.id} ended`);

    res.json(poll);
  } catch (error) {
    console.error('End poll error:', error);
    res.status(500).json({ error: 'Failed to end poll' });
  }
});

// Delete poll
app.delete('/api/polls/:id', (req, res) => {
  try {
    const pollIndex = polls.findIndex(p => p._id === req.params.id);
    if (pollIndex === -1) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    polls.splice(pollIndex, 1);
    console.log(`Poll ${req.params.id} deleted`);

    res.json({ message: 'Poll deleted successfully' });
  } catch (error) {
    console.error('Delete poll error:', error);
    res.status(500).json({ error: 'Failed to delete poll' });
  }
});

// Join session by code
app.get('/api/sessions/:code', (req, res) => {
  const code = req.params.code.toUpperCase();

  // Check polls first
  const poll = polls.find(p => p.sessionCode === code);
  if (poll) {
    return res.json({ ...poll, type: 'poll' });
  }

  // Check quizzes
  const quiz = quizzes.find(q => q.sessionCode === code);
  if (quiz) {
    return res.json({ ...quiz, type: 'quiz' });
  }

  res.status(404).json({ error: 'Session not found' });
});

app.post('/api/polls/:id/vote', (req, res) => {
  try {
    const { optionIndex, socketId } = req.body;
    const poll = polls.find(p => p._id === req.params.id);

    if (!poll || !poll.isActive) {
      return res.status(404).json({ error: 'Poll not found or inactive' });
    }

    const participant = poll.participants.find(p => p.socketId === socketId);
    if (participant && participant.hasVoted) {
      return res.status(400).json({ error: 'You have already voted' });
    }

    if (optionIndex < 0 || optionIndex >= poll.options.length) {
      return res.status(400).json({ error: 'Invalid option' });
    }

    poll.options[optionIndex].voteCount += 1;
    poll.totalVotes += 1;

    if (participant) {
      participant.hasVoted = true;
      participant.selectedOption = optionIndex;
    } else {
      poll.participants.push({
        socketId,
        hasVoted: true,
        selectedOption: optionIndex
      });
    }

    res.json(poll);
  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({ error: 'Failed to process vote' });
  }
});

// Quiz routes
app.post('/api/quizzes', (req, res) => {
  try {
    console.log('Creating quiz with data:', req.body);
    const { question, options, correctAnswer } = req.body;

    if (!question || !options || options.length < 2 || options.length > 5) {
      console.log('Quiz validation failed:', { question, options });
      return res.status(400).json({
        error: 'Question is required and must have 2-5 options'
      });
    }

    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
      console.log('Quiz correct answer validation failed:', { correctAnswer, optionsLength: options.length });
      return res.status(400).json({
        error: 'Valid correct answer index is required'
      });
    }

    const formattedOptions = options.map(option => ({
      text: option,
      selectedCount: 0
    }));

    const sessionCode = generateSessionCode();
    const quiz = {
      _id: `quiz_${quizCounter++}`,
      sessionCode: sessionCode,
      question,
      options: formattedOptions,
      correctAnswer,
      isActive: true,
      showResults: true,
      totalAnswers: 0,
      participants: [],
      createdAt: new Date()
    };

    quizzes.push(quiz);

    const joinUrl = `http://localhost:3000/quiz/${quiz._id}`;

    console.log('Quiz created successfully:', quiz);
    res.status(201).json({
      ...quiz,
      joinUrl: joinUrl
    });
  } catch (error) {
    console.error('Create quiz error:', error);
    res.status(500).json({ error: 'Failed to create quiz' });
  }
});

app.get('/api/quizzes', (req, res) => {
  res.json(quizzes);
});

app.get('/api/quizzes/:id', (req, res) => {
  const quiz = quizzes.find(q => q._id === req.params.id);
  if (!quiz) {
    return res.status(404).json({ error: 'Quiz not found' });
  }
  res.json(quiz);
});

app.post('/api/quizzes/:id/answer', (req, res) => {
  try {
    const { optionIndex, socketId } = req.body;
    const quiz = quizzes.find(q => q._id === req.params.id);

    if (!quiz || !quiz.isActive) {
      return res.status(404).json({ error: 'Quiz not found or inactive' });
    }

    const participant = quiz.participants.find(p => p.socketId === socketId);
    if (participant && participant.hasAnswered) {
      return res.status(400).json({ error: 'You have already answered' });
    }

    if (optionIndex < 0 || optionIndex >= quiz.options.length) {
      return res.status(400).json({ error: 'Invalid option' });
    }

    const isCorrect = optionIndex === quiz.correctAnswer;

    quiz.options[optionIndex].selectedCount += 1;
    quiz.totalAnswers += 1;

    if (participant) {
      participant.hasAnswered = true;
      participant.selectedOption = optionIndex;
      participant.isCorrect = isCorrect;
    } else {
      quiz.participants.push({
        socketId,
        hasAnswered: true,
        selectedOption: optionIndex,
        isCorrect
      });
    }

    res.json({
      quiz,
      isCorrect,
      correctAnswer: quiz.correctAnswer
    });
  } catch (error) {
    console.error('Answer error:', error);
    res.status(500).json({ error: 'Failed to process answer' });
  }
});



// End quiz session
app.patch('/api/quizzes/:id/end', (req, res) => {
  try {
    const quiz = quizzes.find(q => q._id === req.params.id);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    quiz.isActive = false;
    console.log(`Quiz ${req.params.id} ended`);

    res.json(quiz);
  } catch (error) {
    console.error('End quiz error:', error);
    res.status(500).json({ error: 'Failed to end quiz' });
  }
});

// Delete quiz
app.delete('/api/quizzes/:id', (req, res) => {
  try {
    const quizIndex = quizzes.findIndex(q => q._id === req.params.id);
    if (quizIndex === -1) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    quizzes.splice(quizIndex, 1);
    console.log(`Quiz ${req.params.id} deleted`);

    res.json({ message: 'Quiz deleted successfully' });
  } catch (error) {
    console.error('Delete quiz error:', error);
    res.status(500).json({ error: 'Failed to delete quiz' });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-poll', (pollId) => {
    socket.join(`poll_${pollId}`);
    console.log(`User ${socket.id} joined poll: ${pollId}`);
  });

  socket.on('join-quiz', (quizId) => {
    socket.join(`quiz_${quizId}`);
    console.log(`User ${socket.id} joined quiz: ${quizId}`);
  });

  socket.on('vote', async (data) => {
    try {
      const { pollId, optionIndex, socketId } = data;
      const poll = polls.find(p => p._id === pollId);
      
      if (!poll || !poll.isActive) {
        socket.emit('error', { message: 'Invalid poll or poll is inactive' });
        return;
      }

      const participant = poll.participants.find(p => p.socketId === socketId);
      if (participant && participant.hasVoted) {
        socket.emit('error', { message: 'You have already voted' });
        return;
      }

      if (optionIndex < 0 || optionIndex >= poll.options.length) {
        socket.emit('error', { message: 'Invalid option' });
        return;
      }

      poll.options[optionIndex].voteCount += 1;
      poll.totalVotes += 1;

      if (participant) {
        participant.hasVoted = true;
        participant.selectedOption = optionIndex;
      } else {
        poll.participants.push({
          socketId,
          hasVoted: true,
          selectedOption: optionIndex
        });
      }

      io.to(`poll_${pollId}`).emit('poll-update', {
        poll: poll,
        showResults: poll.showResults
      });

      socket.emit('vote-success', { message: 'Vote recorded successfully' });

    } catch (error) {
      console.error('Vote error:', error);
      socket.emit('error', { message: 'Failed to process vote' });
    }
  });

  socket.on('answer', async (data) => {
    try {
      const { quizId, optionIndex, socketId } = data;
      const quiz = quizzes.find(q => q._id === quizId);
      
      if (!quiz || !quiz.isActive) {
        socket.emit('error', { message: 'Invalid quiz or quiz is inactive' });
        return;
      }

      const participant = quiz.participants.find(p => p.socketId === socketId);
      if (participant && participant.hasAnswered) {
        socket.emit('error', { message: 'You have already answered' });
        return;
      }

      if (optionIndex < 0 || optionIndex >= quiz.options.length) {
        socket.emit('error', { message: 'Invalid option' });
        return;
      }

      const isCorrect = optionIndex === quiz.correctAnswer;

      quiz.options[optionIndex].selectedCount += 1;
      quiz.totalAnswers += 1;

      if (participant) {
        participant.hasAnswered = true;
        participant.selectedOption = optionIndex;
        participant.isCorrect = isCorrect;
      } else {
        quiz.participants.push({
          socketId,
          hasAnswered: true,
          selectedOption: optionIndex,
          isCorrect
        });
      }

      socket.emit('answer-feedback', {
        isCorrect,
        correctAnswer: quiz.correctAnswer,
        selectedAnswer: optionIndex
      });

      io.to(`quiz_${quizId}`).emit('quiz-update', {
        quiz: quiz,
        showResults: quiz.showResults
      });

    } catch (error) {
      console.error('Answer error:', error);
      socket.emit('error', { message: 'Failed to process answer' });
    }
  });



  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = 5000;

server.listen(PORT, () => {
  console.log(`Live Polling and Quiz Server running on port ${PORT}`);
  console.log('Using in-memory storage (no MongoDB required)');
});

module.exports = { app, io };
