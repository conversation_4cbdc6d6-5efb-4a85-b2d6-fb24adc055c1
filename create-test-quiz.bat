@echo off
echo ========================================
echo  Creating Test Quiz for Frontend Testing
echo ========================================
echo.

echo 1. Current quizzes in backend:
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo 2. Creating a new test quiz...
curl -s -X POST http://localhost:5000/api/quizzes -H "Content-Type: application/json" -d "{\"question\":\"Frontend Test Quiz - What is 2+2?\",\"options\":[\"3\",\"4\",\"5\"],\"correctAnswer\":1}"
echo.
echo.

echo 3. Updated quizzes list:
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo ========================================
echo  Test quiz created! 
echo  Now test delete and end in frontend.
echo ========================================
pause
