import React, { useState, useEffect } from 'react';
import api from '../api';

const StatusDisplay = () => {
  const [backendStatus, setBackendStatus] = useState('checking');
  const [polls, setPolls] = useState([]);
  const [quizzes, setQuizzes] = useState([]);

  useEffect(() => {
    checkStatus();
    const interval = setInterval(checkStatus, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const checkStatus = async () => {
    try {
      // Test backend health
      await api.get('/health');
      setBackendStatus('connected');
      
      // Get current polls and quizzes
      const [pollsResponse, quizzesResponse] = await Promise.all([
        api.get('/polls'),
        api.get('/quizzes')
      ]);
      
      setPolls(pollsResponse.data);
      setQuizzes(quizzesResponse.data);
    } catch (error) {
      setBackendStatus('disconnected');
    }
  };

  const getStatusColor = () => {
    switch (backendStatus) {
      case 'connected': return '#4CAF50';
      case 'disconnected': return '#f44336';
      default: return '#ff9800';
    }
  };

  const getStatusText = () => {
    switch (backendStatus) {
      case 'connected': return '✅ Backend Connected';
      case 'disconnected': return '⚠️ Using Mock API';
      default: return '🔄 Checking...';
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: `2px solid ${getStatusColor()}`,
      borderRadius: '8px',
      padding: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 1000,
      minWidth: '200px'
    }}>
      <div style={{ 
        color: getStatusColor(), 
        fontWeight: 'bold',
        marginBottom: '5px'
      }}>
        {getStatusText()}
      </div>
      
      <div style={{ fontSize: '12px', color: '#666' }}>
        <div>Polls: {polls.length}</div>
        <div>Quizzes: {quizzes.length}</div>
      </div>

      {polls.length > 0 && (
        <div style={{ marginTop: '10px', fontSize: '11px' }}>
          <strong>Available Poll Codes:</strong>
          {polls.slice(0, 3).map(poll => (
            <div key={poll._id} style={{ 
              background: '#e3f2fd', 
              padding: '2px 4px', 
              margin: '2px 0',
              borderRadius: '3px'
            }}>
              {poll.sessionCode} - {poll.question.substring(0, 20)}...
            </div>
          ))}
        </div>
      )}

      {quizzes.length > 0 && (
        <div style={{ marginTop: '10px', fontSize: '11px' }}>
          <strong>Available Quiz Codes:</strong>
          {quizzes.slice(0, 3).map(quiz => (
            <div key={quiz._id} style={{ 
              background: '#fff3e0', 
              padding: '2px 4px', 
              margin: '2px 0',
              borderRadius: '3px'
            }}>
              {quiz.sessionCode} - {quiz.question.substring(0, 20)}...
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default StatusDisplay;
