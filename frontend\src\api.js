import axios from 'axios';

// Create axios instance with base URL
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api'
    : 'http://localhost:5000/api',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Check if backend is available
let useBackend = true;

// Test backend availability
const testBackend = async () => {
  try {
    await api.get('/health');
    console.log('✅ Backend is available');
    useBackend = true;
  } catch (error) {
    console.log('⚠️ Backend not available, using mock API');
    useBackend = false;
  }
};

// Test backend on startup with retry (limited)
const initializeApi = async () => {
  await testBackend();
  if (!useBackend) {
    // Retry after 2 seconds, but only once
    setTimeout(async () => {
      await testBackend();
    }, 2000);
  }
};

// Only initialize once
if (!window.apiInitialized) {
  window.apiInitialized = true;
  initializeApi();
}

// Enhanced API wrapper - BACKEND ONLY (no mock fallback)
const enhancedApi = {
  async get(url) {
    try {
      const response = await api.get(url);
      console.log('✅ API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('❌ API Error:', error.message, 'for URL:', url);
      throw error; // Don't fall back to mock, throw the error
    }
  },

  async post(url, data) {
    try {
      console.log('📤 API Request:', 'POST', url, data);
      const response = await api.post(url, data);
      console.log('✅ API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('❌ API Error:', error.message, 'for URL:', url);
      throw error; // Don't fall back to mock, throw the error
    }
  },

  async patch(url, data) {
    try {
      console.log('🔄 API Request:', 'PATCH', url, data);
      const response = await api.patch(url, data);
      console.log('✅ API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('❌ API Error:', error.message, 'for URL:', url);
      throw error; // Don't fall back to mock, throw the error
    }
  },

  async delete(url) {
    try {
      console.log('🗑️ API Request:', 'DELETE', url);
      const response = await api.delete(url);
      console.log('✅ API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('❌ API Error:', error.message, 'for URL:', url);
      throw error; // Don't fall back to mock, throw the error
    }
  }
};

export default enhancedApi;
