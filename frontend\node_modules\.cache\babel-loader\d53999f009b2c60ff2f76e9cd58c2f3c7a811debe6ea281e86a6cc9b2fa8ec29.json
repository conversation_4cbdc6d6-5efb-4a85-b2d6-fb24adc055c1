{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor for debugging\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log('API Request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url);\n  return config;\n}, error => {\n  console.error('API Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Add response interceptor for debugging\napi.interceptors.response.use(response => {\n  console.log('API Response:', response.status, response.config.url);\n  return response;\n}, error => {\n  var _error$response, _error$config;\n  console.error('API Response Error:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url, error.message);\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "_error$config", "message"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' \n    ? '/api' \n    : 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptor for debugging\napi.interceptors.request.use(\n  (config) => {\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n  },\n  (error) => {\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for debugging\napi.interceptors.response.use(\n  (response) => {\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n  },\n  (error) => {\n    console.error('API Response Error:', error.response?.status, error.config?.url, error.message);\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1C,MAAM,GACN,2BAA2B;EAC/BC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,cAAc,GAAAF,cAAA,GAAED,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,CAAC;EACrE,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;EAC1C,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlB,GAAG,CAACQ,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACV,MAAM,CAACM,GAAG,CAAC;EAClE,OAAOI,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,aAAA;EACTX,OAAO,CAACK,KAAK,CAAC,qBAAqB,GAAAK,eAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,GAAAE,aAAA,GAAEN,KAAK,CAACP,MAAM,cAAAa,aAAA,uBAAZA,aAAA,CAAcP,GAAG,EAAEC,KAAK,CAACO,OAAO,CAAC;EAC9F,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAelB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}