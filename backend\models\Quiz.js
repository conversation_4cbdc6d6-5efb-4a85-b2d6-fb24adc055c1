const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  question: {
    type: String,
    required: true,
    trim: true
  },
  sessionCode: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    length: 6
  },
  options: [{
    text: {
      type: String,
      required: true,
      trim: true
    },
    selectedCount: {
      type: Number,
      default: 0
    }
  }],
  correctAnswer: {
    type: Number,
    required: true,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  showResults: {
    type: Boolean,
    default: true
  },
  totalAnswers: {
    type: Number,
    default: 0
  },
  participants: [{
    socketId: String,
    hasAnswered: {
      type: Boolean,
      default: false
    },
    selectedOption: {
      type: Number,
      default: null
    },
    isCorrect: {
      type: Boolean,
      default: false
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Quiz', quizSchema);
