@echo off
echo ========================================
echo  Testing Quiz Endpoints
echo ========================================
echo.

echo 1. Getting all current quizzes...
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo 2. Creating a new quiz...
curl -s -X POST http://localhost:5000/api/quizzes -H "Content-Type: application/json" -d "{\"question\":\"Test Quiz for Delete\",\"options\":[\"Option A\",\"Option B\"],\"correctAnswer\":0}"
echo.
echo.

echo 3. Getting all quizzes again...
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo 4. Testing end quiz endpoint (quiz_4)...
curl -s -X PATCH http://localhost:5000/api/quizzes/quiz_4/end
echo.
echo.

echo 5. Testing delete quiz endpoint (quiz_4)...
curl -s -X DELETE http://localhost:5000/api/quizzes/quiz_4
echo.
echo.

echo 6. Final check - getting all quizzes...
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo ========================================
echo  Test Complete!
echo ========================================
pause
