import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import StatusDisplay from './components/StatusDisplay';
import AdminDashboard from './pages/AdminDashboard';
import ParticipantDashboard from './pages/ParticipantDashboard';
import ControlCenter from './pages/ControlCenter';
import PollSession from './pages/PollSession';
import QuizSession from './pages/QuizSession';

function App() {
  return (
    <Router>
      <div className="app">
        <Header />
        <StatusDisplay />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<ParticipantDashboard />} />
            <Route path="/admin" element={<AdminDashboard />} />
            <Route path="/control-center" element={<ControlCenter />} />
            <Route path="/poll/:id" element={<PollSession />} />
            <Route path="/quiz/:id" element={<QuizSession />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
