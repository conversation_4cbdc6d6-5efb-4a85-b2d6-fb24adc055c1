const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const pollRoutes = require('./routes/polls');
const quizRoutes = require('./routes/quizzes');
const Poll = require('./models/Poll');
const Quiz = require('./models/Quiz');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST", "PATCH", "DELETE"]
  }
});

// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));
app.use(express.json());

// Request logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
});

// Routes
app.use('/api/polls', pollRoutes);
app.use('/api/quizzes', quizRoutes);

// Join session by code
app.get('/api/sessions/:code', async (req, res) => {
  try {
    const code = req.params.code.toUpperCase();

    // Check polls first
    const poll = await Poll.findOne({ sessionCode: code });
    if (poll) {
      return res.json({ ...poll.toObject(), type: 'poll' });
    }

    // Check quizzes
    const quiz = await Quiz.findOne({ sessionCode: code });
    if (quiz) {
      return res.json({ ...quiz.toObject(), type: 'quiz' });
    }

    res.status(404).json({ error: 'Session not found' });
  } catch (error) {
    console.error('Session lookup error:', error);
    res.status(500).json({ error: 'Failed to lookup session' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Live Polling and Quiz API is running',
    timestamp: new Date().toISOString()
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join poll room
  socket.on('join-poll', (pollId) => {
    socket.join(`poll_${pollId}`);
    console.log(`User ${socket.id} joined poll: ${pollId}`);
  });

  // Join quiz room
  socket.on('join-quiz', (quizId) => {
    socket.join(`quiz_${quizId}`);
    console.log(`User ${socket.id} joined quiz: ${quizId}`);
  });

  // Handle voting (polls)
  socket.on('vote', async (data) => {
    try {
      const { pollId, optionIndex, socketId } = data;
      const poll = await Poll.findById(pollId);
      
      if (!poll || !poll.isActive) {
        socket.emit('error', { message: 'Invalid poll or poll is inactive' });
        return;
      }

      // Check if user already voted
      const participant = poll.participants.find(p => p.socketId === socketId);
      if (participant && participant.hasVoted) {
        socket.emit('error', { message: 'You have already voted' });
        return;
      }

      // Validate option index
      if (optionIndex < 0 || optionIndex >= poll.options.length) {
        socket.emit('error', { message: 'Invalid option' });
        return;
      }

      // Update vote count
      poll.options[optionIndex].voteCount += 1;
      poll.totalVotes += 1;

      // Add or update participant
      if (participant) {
        participant.hasVoted = true;
        participant.selectedOption = optionIndex;
      } else {
        poll.participants.push({
          socketId,
          hasVoted: true,
          selectedOption: optionIndex
        });
      }

      await poll.save();

      // Emit updated results to all clients in the poll room
      io.to(`poll_${pollId}`).emit('poll-update', {
        poll: poll,
        showResults: poll.showResults
      });

      // Send confirmation to the voter
      socket.emit('vote-success', { message: 'Vote recorded successfully' });

    } catch (error) {
      console.error('Vote error:', error);
      socket.emit('error', { message: 'Failed to process vote' });
    }
  });

  // Handle quiz answers
  socket.on('answer', async (data) => {
    try {
      const { quizId, optionIndex, socketId } = data;
      const quiz = await Quiz.findById(quizId);
      
      if (!quiz || !quiz.isActive) {
        socket.emit('error', { message: 'Invalid quiz or quiz is inactive' });
        return;
      }

      // Check if user already answered
      const participant = quiz.participants.find(p => p.socketId === socketId);
      if (participant && participant.hasAnswered) {
        socket.emit('error', { message: 'You have already answered' });
        return;
      }

      // Validate option index
      if (optionIndex < 0 || optionIndex >= quiz.options.length) {
        socket.emit('error', { message: 'Invalid option' });
        return;
      }

      const isCorrect = optionIndex === quiz.correctAnswer;

      // Update answer count
      quiz.options[optionIndex].selectedCount += 1;
      quiz.totalAnswers += 1;

      // Add or update participant
      if (participant) {
        participant.hasAnswered = true;
        participant.selectedOption = optionIndex;
        participant.isCorrect = isCorrect;
      } else {
        quiz.participants.push({
          socketId,
          hasAnswered: true,
          selectedOption: optionIndex,
          isCorrect
        });
      }

      await quiz.save();

      // Send immediate feedback to the answerer
      socket.emit('answer-feedback', {
        isCorrect,
        correctAnswer: quiz.correctAnswer,
        selectedAnswer: optionIndex
      });

      // Emit updated results to all clients in the quiz room
      io.to(`quiz_${quizId}`).emit('quiz-update', {
        quiz: quiz,
        showResults: quiz.showResults
      });

    } catch (error) {
      console.error('Answer error:', error);
      socket.emit('error', { message: 'Failed to process answer' });
    }
  });

  // Handle admin result visibility toggle
  socket.on('toggle-results', async (data) => {
    try {
      const { type, id } = data;
      
      if (type === 'poll') {
        const poll = await Poll.findById(id);
        if (poll) {
          poll.showResults = !poll.showResults;
          await poll.save();
          
          io.to(`poll_${id}`).emit('results-visibility-changed', {
            showResults: poll.showResults,
            poll: poll
          });
        }
      } else if (type === 'quiz') {
        const quiz = await Quiz.findById(id);
        if (quiz) {
          quiz.showResults = !quiz.showResults;
          await quiz.save();
          
          io.to(`quiz_${id}`).emit('results-visibility-changed', {
            showResults: quiz.showResults,
            quiz: quiz
          });
        }
      }
    } catch (error) {
      console.error('Toggle results error:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    console.log('Continuing without MongoDB - some features may not work');
    // Don't exit, continue running for testing
  }
};

// Connect to database
connectDB();

const PORT = process.env.PORT || 5000;

server.listen(PORT, () => {
  console.log(`Live Polling and Quiz Server running on port ${PORT}`);
});

module.exports = { app, io };
