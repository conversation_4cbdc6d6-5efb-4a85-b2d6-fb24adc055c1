const mongoose = require('mongoose');

const pollSchema = new mongoose.Schema({
  question: {
    type: String,
    required: true,
    trim: true
  },
  sessionCode: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    length: 6
  },
  options: [{
    text: {
      type: String,
      required: true,
      trim: true
    },
    voteCount: {
      type: Number,
      default: 0
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  showResults: {
    type: Boolean,
    default: true
  },
  totalVotes: {
    type: Number,
    default: 0
  },
  participants: [{
    socketId: String,
    hasVoted: {
      type: Boolean,
      default: false
    },
    selectedOption: {
      type: Number,
      default: null
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Poll', pollSchema);
