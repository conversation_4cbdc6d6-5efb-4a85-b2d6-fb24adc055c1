@echo off
echo ========================================
echo  Testing All Poll & Quiz Functionality
echo ========================================
echo.

echo 1. Testing Backend Health...
curl -s http://localhost:5000/api/health
echo.
echo.

echo 2. Testing Poll Creation...
curl -s -X POST http://localhost:5000/api/polls -H "Content-Type: application/json" -d "{\"question\":\"Test Poll?\",\"options\":[\"Option A\",\"Option B\"]}"
echo.
echo.

echo 3. Testing Quiz Creation...
curl -s -X POST http://localhost:5000/api/quizzes -H "Content-Type: application/json" -d "{\"question\":\"Test Quiz?\",\"options\":[\"Correct\",\"Wrong\"],\"correctAnswer\":0}"
echo.
echo.

echo 4. Getting All Polls...
curl -s http://localhost:5000/api/polls
echo.
echo.

echo 5. Getting All Quizzes...
curl -s http://localhost:5000/api/quizzes
echo.
echo.

echo ========================================
echo  Test Complete!
echo ========================================
echo.
echo If you see JSON responses above, the backend is working correctly.
echo Now test the frontend at: http://localhost:3000
echo.
echo Test these features:
echo - Create polls and quizzes
echo - Delete polls and quizzes
echo - End sessions
echo - Hide/Show results
echo - Vote/Answer functionality
echo.
pause
