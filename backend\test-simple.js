console.log('Node.js is working!');
console.log('Current directory:', process.cwd());
console.log('Node version:', process.version);

// Test if we can require basic modules
try {
  const express = require('express');
  console.log('✅ Express is available');
} catch (error) {
  console.log('❌ Express not found:', error.message);
}

try {
  const mongoose = require('mongoose');
  console.log('✅ Mongoose is available');
} catch (error) {
  console.log('❌ Mongoose not found:', error.message);
}

try {
  const socketio = require('socket.io');
  console.log('✅ Socket.IO is available');
} catch (error) {
  console.log('❌ Socket.IO not found:', error.message);
}
