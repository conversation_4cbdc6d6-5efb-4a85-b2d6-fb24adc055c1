import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../api';
// QR Code removed as requested

function AdminDashboard() {
  const [mode, setMode] = useState('poll'); // 'poll' or 'quiz'
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [correctAnswer, setCorrectAnswer] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [createdSession, setCreatedSession] = useState(null);
  const navigate = useNavigate();

  const addOption = () => {
    if (options.length < 5) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
      if (correctAnswer >= newOptions.length) {
        setCorrectAnswer(0);
      }
    }
  };

  const updateOption = (index, value) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    // Validation
    if (!question.trim()) {
      setError('Question is required');
      return;
    }

    const validOptions = options.filter(option => option.trim() !== '');
    if (validOptions.length < 2) {
      setError('At least 2 options are required');
      return;
    }

    if (mode === 'quiz' && (correctAnswer >= validOptions.length || correctAnswer < 0)) {
      setError('Please select a valid correct answer');
      return;
    }

    setLoading(true);
    
    try {
      const endpoint = mode === 'poll' ? '/polls' : '/quizzes';
      const payload = {
        question: question.trim(),
        options: validOptions
      };

      if (mode === 'quiz') {
        payload.correctAnswer = correctAnswer;
      }

      const response = await api.post(endpoint, payload);
      setCreatedSession(response.data);

      // Auto-enable results display for immediate testing
      setTimeout(async () => {
        try {
          await api.patch(`/${mode}s/${response.data._id}/toggle-results`);
          setCreatedSession(prev => ({ ...prev, showResults: true }));
        } catch (err) {
          console.error('Failed to enable results:', err);
        }
      }, 1000);

      // Reset form
      setQuestion('');
      setOptions(['', '']);
      setCorrectAnswer(0);
      
    } catch (err) {
      setError(err.response?.data?.error || `Failed to create ${mode}`);
    } finally {
      setLoading(false);
    }
  };

  const joinAsParticipant = () => {
    if (createdSession) {
      const url = `/${mode}/${createdSession._id}`;
      window.open(url, '_blank');
    }
  };

  const goToControlCenter = () => {
    navigate('/control-center');
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1 className="dashboard-title">Create New Session</h1>
        <p className="dashboard-subtitle">
          Create interactive polls and quizzes with real-time results
        </p>
      </div>

      {/* Mode Toggle */}
      <div className="mode-toggle">
        <button 
          className={`mode-btn ${mode === 'poll' ? 'active' : ''}`}
          onClick={() => setMode('poll')}
        >
          📊 Poll
        </button>
        <button 
          className={`mode-btn ${mode === 'quiz' ? 'active' : ''}`}
          onClick={() => setMode('quiz')}
        >
          🧠 Quiz
        </button>
      </div>

      {/* Creation Form */}
      <div className="creation-form">
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="question">Question</label>
            <input
              type="text"
              id="question"
              className="form-input"
              placeholder={`Enter your ${mode} question...`}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label>Options</label>
            {options.map((option, index) => (
              <div key={index} className="option-input-group">
                <div className="option-letter">{String.fromCharCode(65 + index)}</div>
                <input
                  type="text"
                  className="form-input option-input"
                  placeholder={`Option ${String.fromCharCode(65 + index)}`}
                  value={option}
                  onChange={(e) => updateOption(index, e.target.value)}
                  required
                />
                {mode === 'quiz' && (
                  <label className="correct-answer-radio">
                    <input
                      type="radio"
                      name="correctAnswer"
                      checked={correctAnswer === index}
                      onChange={() => setCorrectAnswer(index)}
                    />
                    <span className="radio-label">Correct</span>
                  </label>
                )}
                {options.length > 2 && (
                  <button
                    type="button"
                    className="remove-option-btn"
                    onClick={() => removeOption(index)}
                  >
                    ✕
                  </button>
                )}
              </div>
            ))}
            
            {options.length < 5 && (
              <button
                type="button"
                className="add-option-btn"
                onClick={addOption}
              >
                + Add Option
              </button>
            )}
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="form-actions">
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? 'Creating...' : `Create ${mode.charAt(0).toUpperCase() + mode.slice(1)}`}
            </button>
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={goToControlCenter}
            >
              Go to Control Center
            </button>
          </div>
        </form>
      </div>

      {/* Created Session Display */}
      {createdSession && (
        <div className="created-session">
          <h3>🎉 {mode.charAt(0).toUpperCase() + mode.slice(1)} Created Successfully!</h3>

          <div className="session-info">
            <div className="session-details">
              <h4>{createdSession.question}</h4>
              <div className="session-code-display">
                <h3>📋 Session Code: <span className="session-code">{createdSession.sessionCode}</span></h3>
                <p>Share this code with participants to join</p>
              </div>
              <p>Session ID: {createdSession._id}</p>
              <p>Created: {new Date(createdSession.createdAt).toLocaleString()}</p>
              <p>Share URL: {window.location.origin}/{mode}/{createdSession._id}</p>
              <p className="results-status">
                Results: {createdSession.showResults ? '✅ Visible to participants' : '⏳ Hidden from participants'}
              </p>
            </div>
          </div>

          <div className="session-actions">
            <button
              className="btn btn-primary"
              onClick={joinAsParticipant}
            >
              Join as Participant
            </button>
            <button
              className="btn btn-secondary"
              onClick={goToControlCenter}
            >
              Manage in Control Center
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminDashboard;
