const http = require('http');
const url = require('url');

// In-memory storage
let polls = [];
let quizzes = [];
let pollCounter = 1;
let quizCounter = 1;

// Generate session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Add initial test data
polls.push({
  _id: 'poll_1',
  sessionCode: 'TEST01',
  question: 'What is your favorite color?',
  options: [
    { text: 'Red', voteCount: 3 },
    { text: 'Blue', voteCount: 5 },
    { text: 'Green', voteCount: 2 }
  ],
  isActive: true,
  showResults: true,
  totalVotes: 10,
  participants: [],
  createdAt: new Date()
});

quizzes.push({
  _id: 'quiz_1',
  sessionCode: 'QUIZ01',
  question: 'What is 2 + 2?',
  options: [
    { text: '3', selectedCount: 1 },
    { text: '4', selectedCount: 8 },
    { text: '5', selectedCount: 0 },
    { text: '6', selectedCount: 1 }
  ],
  correctAnswer: 1,
  isActive: true,
  showResults: true,
  totalAnswers: 10,
  participants: [],
  createdAt: new Date()
});

function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = body ? JSON.parse(body) : {};
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

function sendResponse(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path} - ${new Date().toISOString()}`);

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Health check
  if (path === '/api/health' && method === 'GET') {
    sendResponse(res, 200, {
      success: true,
      message: 'Live Polling and Quiz API is running',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Create poll
  if (path === '/api/polls' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendResponse(res, 400, { error: 'Invalid JSON' });
        return;
      }

      const { question, options } = body;
      
      if (!question || !options || options.length < 2 || options.length > 5) {
        sendResponse(res, 400, { error: 'Question is required and must have 2-5 options' });
        return;
      }

      const formattedOptions = options.map(option => ({
        text: option,
        voteCount: 0
      }));

      const sessionCode = generateSessionCode();
      const poll = {
        _id: `poll_${pollCounter++}`,
        sessionCode: sessionCode,
        question,
        options: formattedOptions,
        isActive: true,
        showResults: true,
        totalVotes: 0,
        participants: [],
        createdAt: new Date()
      };

      polls.push(poll);
      console.log('Poll created:', poll);
      
      sendResponse(res, 201, {
        ...poll,
        joinUrl: `http://localhost:3000/poll/${poll._id}`
      });
    });
    return;
  }

  // Get polls
  if (path === '/api/polls' && method === 'GET') {
    sendResponse(res, 200, polls);
    return;
  }

  // Create quiz
  if (path === '/api/quizzes' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendResponse(res, 400, { error: 'Invalid JSON' });
        return;
      }

      const { question, options, correctAnswer } = body;
      
      if (!question || !options || options.length < 2 || options.length > 5) {
        sendResponse(res, 400, { error: 'Question is required and must have 2-5 options' });
        return;
      }

      if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
        sendResponse(res, 400, { error: 'Valid correct answer index is required' });
        return;
      }

      const formattedOptions = options.map(option => ({
        text: option,
        selectedCount: 0
      }));

      const sessionCode = generateSessionCode();
      const quiz = {
        _id: `quiz_${quizCounter++}`,
        sessionCode: sessionCode,
        question,
        options: formattedOptions,
        correctAnswer,
        isActive: true,
        showResults: true,
        totalAnswers: 0,
        participants: [],
        createdAt: new Date()
      };

      quizzes.push(quiz);
      console.log('Quiz created:', quiz);
      
      sendResponse(res, 201, {
        ...quiz,
        joinUrl: `http://localhost:3000/quiz/${quiz._id}`
      });
    });
    return;
  }

  // Get quizzes
  if (path === '/api/quizzes' && method === 'GET') {
    sendResponse(res, 200, quizzes);
    return;
  }

  // Session lookup
  if (path.startsWith('/api/sessions/') && method === 'GET') {
    const code = path.split('/')[3].toUpperCase();
    console.log('Looking up session code:', code);

    const poll = polls.find(p => p.sessionCode === code);
    if (poll) {
      sendResponse(res, 200, { ...poll, type: 'poll' });
      return;
    }

    const quiz = quizzes.find(q => q.sessionCode === code);
    if (quiz) {
      sendResponse(res, 200, { ...quiz, type: 'quiz' });
      return;
    }

    sendResponse(res, 404, { error: 'Session not found' });
    return;
  }

  // 404 for all other routes
  sendResponse(res, 404, { error: 'Route not found' });
});

const PORT = 5000;

server.listen(PORT, () => {
  console.log(`✅ Live Polling and Quiz Server running on port ${PORT}`);
  console.log(`✅ Health check: http://localhost:${PORT}/api/health`);
  console.log(`✅ Test session codes: TEST01 (poll), QUIZ01 (quiz)`);
  console.log(`✅ CORS enabled for all origins`);
});

// Handle server errors
server.on('error', (err) => {
  console.error('❌ Server error:', err);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled rejection:', err);
});
