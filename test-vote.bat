@echo off
echo Testing Vote Functionality...
echo.
echo 1. Starting backend server...
start "Backend Server" cmd /k "cd backend && node server-simple.js"
echo.
echo 2. Waiting 3 seconds for server to start...
timeout /t 3 /nobreak > nul
echo.
echo 3. Testing health endpoint...
curl -s http://localhost:5000/api/health
echo.
echo.
echo 4. Backend should now be running on http://localhost:5000
echo 5. Frontend should be running on http://localhost:3000
echo.
echo If you see "Live Polling and Quiz API is running" above, the backend is working!
echo.
pause
