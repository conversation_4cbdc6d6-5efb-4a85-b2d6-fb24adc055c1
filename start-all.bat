@echo off
echo ========================================
echo  Starting Live Poll & Quiz Application
echo ========================================
echo.

echo 1. Starting Backend Server...
start "Backend Server" cmd /k "cd backend && echo Starting Backend on Port 5000... && node server-simple.js"

echo 2. Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak > nul

echo 3. Starting Frontend Development Server...
start "Frontend Server" cmd /k "cd frontend && echo Starting Frontend on Port 3000... && npm start"

echo.
echo ========================================
echo  Both servers are starting up!
echo ========================================
echo.
echo Backend:  http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo The frontend will automatically open in your browser.
echo Both terminal windows will remain open for monitoring.
echo.
echo Press any key to close this window...
pause > nul
