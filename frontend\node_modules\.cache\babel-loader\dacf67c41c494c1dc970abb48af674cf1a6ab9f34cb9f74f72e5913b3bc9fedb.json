{"ast": null, "code": "import { io } from 'socket.io-client';\nconst socket = io('http://localhost:5000', {\n  autoConnect: true,\n  reconnection: true,\n  reconnectionDelay: 1000,\n  reconnectionAttempts: 5,\n  maxReconnectionAttempts: 5\n});\n\n// Add connection event listeners for debugging\nsocket.on('connect', () => {\n  console.log('✅ Socket.IO connected successfully!', socket.id);\n});\nsocket.on('disconnect', () => {\n  console.log('❌ Socket.IO disconnected');\n});\nsocket.on('connect_error', error => {\n  console.error('❌ Socket.IO connection error:', error);\n});\nexport default socket;", "map": {"version": 3, "names": ["io", "socket", "autoConnect", "reconnection", "reconnectionDelay", "reconnectionAttempts", "maxReconnectionAttempts", "on", "console", "log", "id", "error"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nconst socket = io('http://localhost:5000', {\n  autoConnect: true,\n  reconnection: true,\n  reconnectionDelay: 1000,\n  reconnectionAttempts: 5,\n  maxReconnectionAttempts: 5\n});\n\n// Add connection event listeners for debugging\nsocket.on('connect', () => {\n  console.log('✅ Socket.IO connected successfully!', socket.id);\n});\n\nsocket.on('disconnect', () => {\n  console.log('❌ Socket.IO disconnected');\n});\n\nsocket.on('connect_error', (error) => {\n  console.error('❌ Socket.IO connection error:', error);\n});\n\nexport default socket;\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,kBAAkB;AAErC,MAAMC,MAAM,GAAGD,EAAE,CAAC,uBAAuB,EAAE;EACzCE,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;;AAEF;AACAL,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE,MAAM;EACzBC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAER,MAAM,CAACS,EAAE,CAAC;AAC/D,CAAC,CAAC;AAEFT,MAAM,CAACM,EAAE,CAAC,YAAY,EAAE,MAAM;EAC5BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;AACzC,CAAC,CAAC;AAEFR,MAAM,CAACM,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;EACpCH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;AACvD,CAAC,CAAC;AAEF,eAAeV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}