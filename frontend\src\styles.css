/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  text-decoration: none;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #764ba2;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #666;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Dashboard Styles */
.dashboard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1rem;
}

/* Mode Toggle */
.mode-toggle {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.mode-btn {
  padding: 1rem 2rem;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.mode-btn:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.mode-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* Form Styles */
.creation-form,
.join-section {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.session-input {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* Option Input Groups */
.option-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.option-letter {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.option-input {
  flex: 1;
}

.correct-answer-radio {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  white-space: nowrap;
}

.correct-answer-radio input[type="radio"] {
  margin: 0;
}

.radio-label {
  font-size: 0.9rem;
  color: #666;
}

.remove-option-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.remove-option-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.add-option-btn {
  background: #2ed573;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-option-btn:hover {
  background: #26d065;
  transform: translateY(-2px);
}

/* Buttons */
.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-success {
  background: #2ed573;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #26d065;
  transform: translateY(-2px);
}

.btn-warning {
  background: #ffa502;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #ff9500;
  transform: translateY(-2px);
}

.btn-danger {
  background: #ff4757;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #ff3742;
  transform: translateY(-2px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.join-btn {
  width: 100%;
  margin-top: 1rem;
}

/* Join Section */
.join-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  margin-bottom: 2rem;
}

.join-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.join-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.join-card p {
  color: #666;
  margin-bottom: 1.5rem;
}

.join-form {
  max-width: 300px;
  margin: 0 auto;
}

/* Info Section */
.info-section {
  margin: 2rem 0;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.info-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.info-card h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.info-card p {
  color: #666;
  font-size: 0.9rem;
}

/* Instructions */
.instructions {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
  margin-bottom: 1rem;
  color: #333;
}

.instructions ol {
  padding-left: 1.5rem;
}

.instructions li {
  margin-bottom: 0.5rem;
  color: #666;
}

/* Created Session */
.created-session {
  background: linear-gradient(135deg, #2ed573, #26d065);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  margin-top: 2rem;
  text-align: center;
}

.session-code-display {
  background: rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  border-radius: 12px;
  margin: 1rem 0;
}

.session-code {
  font-size: 2rem;
  font-weight: 900;
  letter-spacing: 4px;
  background: rgba(255, 255, 255, 0.9);
  color: #2ed573;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  display: inline-block;
  margin: 0.5rem 0;
}

.created-session h3 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.session-info {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  align-items: center;
  margin-bottom: 1.5rem;
}

.session-details h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.session-details p {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.qr-section {
  text-align: center;
}

.qr-section h4 {
  margin-bottom: 1rem;
}

.qr-code {
  background: white;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.join-url {
  font-size: 0.9rem;
  opacity: 0.9;
  word-break: break-all;
}

.session-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Session Container */
.session-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.session-type {
  font-size: 1.2rem;
  font-weight: 600;
  color: #667eea;
}

.session-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.session-status.active {
  background: #2ed573;
  color: white;
}

.session-status.ended {
  background: #ff4757;
  color: white;
}

/* Question Card */
.question-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.question-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.instruction {
  text-align: center;
  color: #666;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Options */
.options-list {
  list-style: none;
  display: grid;
  gap: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: #e3f2fd;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.option-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

/* Feedback */
.feedback-container {
  text-align: center;
  margin-top: 1rem;
}

.feedback {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  font-weight: 500;
}

.feedback.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback.correct {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback.incorrect {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.feedback.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.feedback-icon {
  font-size: 1.5rem;
}

.feedback-text {
  text-align: left;
}

/* Results */
.results-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.results-hidden {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  text-align: center;
  border: 2px dashed #dee2e6;
}

.results-hidden h3 {
  color: #6c757d;
  margin-bottom: 1rem;
}

.results-hidden p {
  color: #6c757d;
  font-style: italic;
}

.results-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.waiting-message {
  text-align: center;
  color: #666;
  font-style: italic;
}

.quiz-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  text-align: center;
}

.stat-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

.results-list {
  display: grid;
  gap: 1rem;
}

.result-item {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
}

.result-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-count {
  font-weight: 600;
  color: #666;
}

.result-bar {
  height: 8px;
  background: #e9ecef;
  position: relative;
}

.result-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 0.5rem;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 40px;
}

.result-fill.correct {
  background: #2ed573;
}

.result-fill.user-selected {
  background: #ffa502;
}

/* Session Info */
.session-info {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 1rem;
}

.session-info p {
  margin: 0.25rem 0;
  color: #666;
  font-size: 0.9rem;
}

/* Control Center */
.control-center {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.sessions-grid {
  display: grid;
  gap: 2rem;
}

.sessions-section h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.sessions-list {
  display: grid;
  gap: 1.5rem;
}

.session-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.session-card:hover {
  transform: translateY(-3px);
}

.session-card .session-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

.session-type-badge {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.session-question {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
}

.session-code-info {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.session-code-label {
  font-size: 0.9rem;
  color: #666;
  margin-right: 0.5rem;
}

.session-code-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #667eea;
  letter-spacing: 2px;
}

.session-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
}

.session-options {
  margin-bottom: 1rem;
}

.option-result {
  margin-bottom: 0.5rem;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.option-text {
  font-size: 0.9rem;
  color: #333;
}

.option-count {
  font-size: 0.8rem;
  color: #666;
  font-weight: 600;
}

.option-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.option-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.option-fill.correct {
  background: #2ed573;
}

.session-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.empty-state p {
  margin-bottom: 1rem;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #333;
}

.modal-body .qr-section {
  text-align: center;
}

.modal-body .qr-code {
  margin: 1rem 0;
}

.modal-body .join-url {
  word-break: break-all;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-family: monospace;
  font-size: 0.9rem;
}

.session-id {
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 8px;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
  
  .dashboard {
    padding: 1.5rem;
  }
  
  .dashboard-title {
    font-size: 2rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav {
    gap: 1rem;
  }
  
  .mode-toggle {
    flex-direction: column;
    align-items: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .session-info {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .session-actions {
    flex-direction: column;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
  
  .quiz-stats {
    grid-template-columns: 1fr;
  }
  
  .option-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .correct-answer-radio {
    justify-content: center;
  }
}
