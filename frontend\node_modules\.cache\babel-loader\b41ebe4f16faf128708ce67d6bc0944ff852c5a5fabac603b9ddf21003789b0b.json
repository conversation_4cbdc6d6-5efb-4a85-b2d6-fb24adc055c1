{"ast": null, "code": "import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper - BACKEND ONLY (no mock fallback)\nconst enhancedApi = {\n  async get(url) {\n    try {\n      const response = await api.get(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n  async post(url, data) {\n    try {\n      console.log('📤 API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n  async patch(url, data) {\n    try {\n      console.log('🔄 API Request:', 'PATCH', url, data);\n      const response = await api.patch(url, data);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n  async delete(url) {\n    try {\n      console.log('🗑️ API Request:', 'DELETE', url);\n      const response = await api.delete(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  }\n};\nexport default enhancedApi;", "map": {"version": 3, "names": ["axios", "mockApi", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "useBackend", "testBackend", "get", "console", "log", "error", "initializeApi", "setTimeout", "window", "apiInitialized", "enhancedApi", "url", "response", "status", "message", "post", "data", "patch", "delete"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production'\n    ? '/api'\n    : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper - BACKEND ONLY (no mock fallback)\nconst enhancedApi = {\n  async get(url) {\n    try {\n      const response = await api.get(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n\n  async post(url, data) {\n    try {\n      console.log('📤 API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n\n  async patch(url, data) {\n    try {\n      console.log('🔄 API Request:', 'PATCH', url, data);\n      const response = await api.patch(url, data);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  },\n\n  async delete(url) {\n    try {\n      console.log('🗑️ API Request:', 'DELETE', url);\n      const response = await api.delete(url);\n      console.log('✅ API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('❌ API Error:', error.message, 'for URL:', url);\n      throw error; // Don't fall back to mock, throw the error\n    }\n  }\n};\n\nexport default enhancedApi;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1C,MAAM,GACN,2BAA2B;EAC/BC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,UAAU,GAAG,IAAI;;AAErB;AACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EAC9B,IAAI;IACF,MAAMT,GAAG,CAACU,GAAG,CAAC,SAAS,CAAC;IACxBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCJ,UAAU,GAAG,IAAI;EACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDJ,UAAU,GAAG,KAAK;EACpB;AACF,CAAC;;AAED;AACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,MAAML,WAAW,CAAC,CAAC;EACnB,IAAI,CAACD,UAAU,EAAE;IACf;IACAO,UAAU,CAAC,YAAY;MACrB,MAAMN,WAAW,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC;;AAED;AACA,IAAI,CAACO,MAAM,CAACC,cAAc,EAAE;EAC1BD,MAAM,CAACC,cAAc,GAAG,IAAI;EAC5BH,aAAa,CAAC,CAAC;AACjB;;AAEA;AACA,MAAMI,WAAW,GAAG;EAClB,MAAMR,GAAGA,CAACS,GAAG,EAAE;IACb,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,GAAG,CAACU,GAAG,CAACS,GAAG,CAAC;MACnCR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACS,OAAO,EAAE,UAAU,EAAEH,GAAG,CAAC;MAC7D,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMU,IAAIA,CAACJ,GAAG,EAAEK,IAAI,EAAE;IACpB,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,MAAM,EAAEO,GAAG,EAAEK,IAAI,CAAC;MACjD,MAAMJ,QAAQ,GAAG,MAAMpB,GAAG,CAACuB,IAAI,CAACJ,GAAG,EAAEK,IAAI,CAAC;MAC1Cb,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACS,OAAO,EAAE,UAAU,EAAEH,GAAG,CAAC;MAC7D,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMY,KAAKA,CAACN,GAAG,EAAEK,IAAI,EAAE;IACrB,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,OAAO,EAAEO,GAAG,EAAEK,IAAI,CAAC;MAClD,MAAMJ,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,KAAK,CAACN,GAAG,EAAEK,IAAI,CAAC;MAC3Cb,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACS,OAAO,EAAE,UAAU,EAAEH,GAAG,CAAC;MAC7D,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMa,MAAMA,CAACP,GAAG,EAAE;IAChB,IAAI;MACFR,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,EAAEO,GAAG,CAAC;MAC9C,MAAMC,QAAQ,GAAG,MAAMpB,GAAG,CAAC0B,MAAM,CAACP,GAAG,CAAC;MACtCR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAACC,MAAM,EAAEF,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACS,OAAO,EAAE,UAAU,EAAEH,GAAG,CAAC;MAC7D,MAAMN,KAAK,CAAC,CAAC;IACf;EACF;AACF,CAAC;AAED,eAAeK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}