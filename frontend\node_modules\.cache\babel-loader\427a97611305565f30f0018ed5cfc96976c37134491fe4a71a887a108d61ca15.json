{"ast": null, "code": "import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper with fallback to mock\nconst enhancedApi = {\n  async get(url) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.getPolls();\n      if (url === '/quizzes') return mockApi.getQuizzes();\n      if (url.startsWith('/polls/')) {\n        const id = url.split('/')[2];\n        return mockApi.getPoll(id);\n      }\n      if (url.startsWith('/quizzes/')) {\n        const id = url.split('/')[2];\n        return mockApi.getQuiz(id);\n      }\n      if (url.startsWith('/sessions/')) {\n        const code = url.split('/')[2];\n        return mockApi.getSession(code);\n      }\n    }\n    try {\n      const response = await api.get(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.get(url); // Retry with mock\n    }\n  },\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n  },\n  async delete(url) {\n    if (!useBackend) {\n      console.log('Mock API: DELETE not implemented for', url);\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n    try {\n      const response = await api.delete(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return {\n        data: {\n          success: true\n        }\n      };\n    }\n  }\n};\nexport default enhancedApi;", "map": {"version": 3, "names": ["axios", "mockApi", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "useBackend", "testBackend", "get", "console", "log", "error", "initializeApi", "setTimeout", "window", "apiInitialized", "enhancedApi", "url", "getPolls", "getQuizzes", "startsWith", "id", "split", "getPoll", "getQuiz", "code", "getSession", "response", "status", "message", "post", "data", "createPoll", "createQuiz", "includes", "pollId", "vote", "quizId", "answer", "patch", "success", "delete"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\nimport mockApi from './mockApi';\n\n// Create axios instance with base URL\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production'\n    ? '/api'\n    : 'http://localhost:5000/api',\n  timeout: 5000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Check if backend is available\nlet useBackend = true;\n\n// Test backend availability\nconst testBackend = async () => {\n  try {\n    await api.get('/health');\n    console.log('✅ Backend is available');\n    useBackend = true;\n  } catch (error) {\n    console.log('⚠️ Backend not available, using mock API');\n    useBackend = false;\n  }\n};\n\n// Test backend on startup with retry (limited)\nconst initializeApi = async () => {\n  await testBackend();\n  if (!useBackend) {\n    // Retry after 2 seconds, but only once\n    setTimeout(async () => {\n      await testBackend();\n    }, 2000);\n  }\n};\n\n// Only initialize once\nif (!window.apiInitialized) {\n  window.apiInitialized = true;\n  initializeApi();\n}\n\n// Enhanced API wrapper with fallback to mock\nconst enhancedApi = {\n  async get(url) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.getPolls();\n      if (url === '/quizzes') return mockApi.getQuizzes();\n      if (url.startsWith('/polls/')) {\n        const id = url.split('/')[2];\n        return mockApi.getPoll(id);\n      }\n      if (url.startsWith('/quizzes/')) {\n        const id = url.split('/')[2];\n        return mockApi.getQuiz(id);\n      }\n      if (url.startsWith('/sessions/')) {\n        const code = url.split('/')[2];\n        return mockApi.getSession(code);\n      }\n    }\n\n    try {\n      const response = await api.get(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.get(url); // Retry with mock\n    }\n  },\n\n  async post(url, data) {\n    if (!useBackend) {\n      // Handle mock API calls\n      if (url === '/polls') return mockApi.createPoll(data);\n      if (url === '/quizzes') return mockApi.createQuiz(data);\n      if (url.includes('/vote')) {\n        const pollId = url.split('/')[1];\n        return mockApi.vote(pollId, data);\n      }\n      if (url.includes('/answer')) {\n        const quizId = url.split('/')[1];\n        return mockApi.answer(quizId, data);\n      }\n    }\n\n    try {\n      console.log('API Request:', 'POST', url, data);\n      const response = await api.post(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message, 'Falling back to mock API');\n      useBackend = false;\n      return this.post(url, data); // Retry with mock\n    }\n  },\n\n  async patch(url, data) {\n    if (!useBackend) {\n      console.log('Mock API: PATCH not implemented for', url);\n      return { data: { success: true } };\n    }\n\n    try {\n      const response = await api.patch(url, data);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return { data: { success: true } };\n    }\n  },\n\n  async delete(url) {\n    if (!useBackend) {\n      console.log('Mock API: DELETE not implemented for', url);\n      return { data: { success: true } };\n    }\n\n    try {\n      const response = await api.delete(url);\n      console.log('API Response:', response.status, url);\n      return response;\n    } catch (error) {\n      console.error('API Error:', error.message);\n      useBackend = false;\n      return { data: { success: true } };\n    }\n  }\n};\n\nexport default enhancedApi;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1C,MAAM,GACN,2BAA2B;EAC/BC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,UAAU,GAAG,IAAI;;AAErB;AACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EAC9B,IAAI;IACF,MAAMT,GAAG,CAACU,GAAG,CAAC,SAAS,CAAC;IACxBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCJ,UAAU,GAAG,IAAI;EACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDJ,UAAU,GAAG,KAAK;EACpB;AACF,CAAC;;AAED;AACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,MAAML,WAAW,CAAC,CAAC;EACnB,IAAI,CAACD,UAAU,EAAE;IACf;IACAO,UAAU,CAAC,YAAY;MACrB,MAAMN,WAAW,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC;;AAED;AACA,IAAI,CAACO,MAAM,CAACC,cAAc,EAAE;EAC1BD,MAAM,CAACC,cAAc,GAAG,IAAI;EAC5BH,aAAa,CAAC,CAAC;AACjB;;AAEA;AACA,MAAMI,WAAW,GAAG;EAClB,MAAMR,GAAGA,CAACS,GAAG,EAAE;IACb,IAAI,CAACX,UAAU,EAAE;MACf;MACA,IAAIW,GAAG,KAAK,QAAQ,EAAE,OAAOpB,OAAO,CAACqB,QAAQ,CAAC,CAAC;MAC/C,IAAID,GAAG,KAAK,UAAU,EAAE,OAAOpB,OAAO,CAACsB,UAAU,CAAC,CAAC;MACnD,IAAIF,GAAG,CAACG,UAAU,CAAC,SAAS,CAAC,EAAE;QAC7B,MAAMC,EAAE,GAAGJ,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAOzB,OAAO,CAAC0B,OAAO,CAACF,EAAE,CAAC;MAC5B;MACA,IAAIJ,GAAG,CAACG,UAAU,CAAC,WAAW,CAAC,EAAE;QAC/B,MAAMC,EAAE,GAAGJ,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAOzB,OAAO,CAAC2B,OAAO,CAACH,EAAE,CAAC;MAC5B;MACA,IAAIJ,GAAG,CAACG,UAAU,CAAC,YAAY,CAAC,EAAE;QAChC,MAAMK,IAAI,GAAGR,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9B,OAAOzB,OAAO,CAAC6B,UAAU,CAACD,IAAI,CAAC;MACjC;IACF;IAEA,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM7B,GAAG,CAACU,GAAG,CAACS,GAAG,CAAC;MACnCR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiB,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACkB,OAAO,EAAE,0BAA0B,CAAC;MACtEvB,UAAU,GAAG,KAAK;MAClB,OAAO,IAAI,CAACE,GAAG,CAACS,GAAG,CAAC,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMa,IAAIA,CAACb,GAAG,EAAEc,IAAI,EAAE;IACpB,IAAI,CAACzB,UAAU,EAAE;MACf;MACA,IAAIW,GAAG,KAAK,QAAQ,EAAE,OAAOpB,OAAO,CAACmC,UAAU,CAACD,IAAI,CAAC;MACrD,IAAId,GAAG,KAAK,UAAU,EAAE,OAAOpB,OAAO,CAACoC,UAAU,CAACF,IAAI,CAAC;MACvD,IAAId,GAAG,CAACiB,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzB,MAAMC,MAAM,GAAGlB,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAOzB,OAAO,CAACuC,IAAI,CAACD,MAAM,EAAEJ,IAAI,CAAC;MACnC;MACA,IAAId,GAAG,CAACiB,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC3B,MAAMG,MAAM,GAAGpB,GAAG,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,OAAOzB,OAAO,CAACyC,MAAM,CAACD,MAAM,EAAEN,IAAI,CAAC;MACrC;IACF;IAEA,IAAI;MACFtB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAEO,GAAG,EAAEc,IAAI,CAAC;MAC9C,MAAMJ,QAAQ,GAAG,MAAM7B,GAAG,CAACgC,IAAI,CAACb,GAAG,EAAEc,IAAI,CAAC;MAC1CtB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiB,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACkB,OAAO,EAAE,0BAA0B,CAAC;MACtEvB,UAAU,GAAG,KAAK;MAClB,OAAO,IAAI,CAACwB,IAAI,CAACb,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMQ,KAAKA,CAACtB,GAAG,EAAEc,IAAI,EAAE;IACrB,IAAI,CAACzB,UAAU,EAAE;MACfG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEO,GAAG,CAAC;MACvD,OAAO;QAAEc,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;IAEA,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM7B,GAAG,CAACyC,KAAK,CAACtB,GAAG,EAAEc,IAAI,CAAC;MAC3CtB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiB,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACkB,OAAO,CAAC;MAC1CvB,UAAU,GAAG,KAAK;MAClB,OAAO;QAAEyB,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,MAAMA,CAACxB,GAAG,EAAE;IAChB,IAAI,CAACX,UAAU,EAAE;MACfG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEO,GAAG,CAAC;MACxD,OAAO;QAAEc,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;IAEA,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM7B,GAAG,CAAC2C,MAAM,CAACxB,GAAG,CAAC;MACtCR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiB,QAAQ,CAACC,MAAM,EAAEX,GAAG,CAAC;MAClD,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACkB,OAAO,CAAC;MAC1CvB,UAAU,GAAG,KAAK;MAClB,OAAO;QAAEyB,IAAI,EAAE;UAAES,OAAO,EAAE;QAAK;MAAE,CAAC;IACpC;EACF;AACF,CAAC;AAED,eAAexB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}