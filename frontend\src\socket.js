import { io } from 'socket.io-client';

const socket = io('http://localhost:5000', {
  autoConnect: true,
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
  maxReconnectionAttempts: 5,
  timeout: 5000
});

// Add connection event listeners for debugging
socket.on('connect', () => {
  console.log('✅ Socket.IO connected successfully!', socket.id);
});

socket.on('disconnect', (reason) => {
  console.log('❌ Socket.IO disconnected:', reason);
});

socket.on('connect_error', (error) => {
  console.error('❌ Socket.IO connection error:', error);
  console.error('❌ Make sure backend is running on http://localhost:5000');
});

socket.on('error', (error) => {
  console.error('❌ Socket.IO error:', error);
});

// Add vote-specific error handling
socket.on('vote-error', (error) => {
  console.error('❌ Vote error:', error);
  alert(`Vote failed: ${error.message || 'Unknown error'}`);
});

export default socket;
