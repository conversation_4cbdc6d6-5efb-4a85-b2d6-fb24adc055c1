{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lpqa cpy\\\\frontend\\\\src\\\\pages\\\\QuizSession.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport socket from '../socket';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction QuizSession() {\n  _s();\n  var _quiz$options$feedbac, _quiz$participants;\n  const {\n    id\n  } = useParams();\n  const [quiz, setQuiz] = useState(null);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [hasAnswered, setHasAnswered] = useState(false);\n  const [feedback, setFeedback] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const fetchQuiz = useCallback(async () => {\n    try {\n      var _response$data$partic;\n      const response = await axios.get(`/api/quizzes/${id}`);\n      setQuiz(response.data);\n\n      // Check if user already answered\n      const participant = (_response$data$partic = response.data.participants) === null || _response$data$partic === void 0 ? void 0 : _response$data$partic.find(p => p.socketId === socket.id);\n      if (participant && participant.hasAnswered) {\n        setHasAnswered(true);\n        setSelectedOption(participant.selectedOption);\n        setFeedback({\n          isCorrect: participant.isCorrect,\n          correctAnswer: response.data.correctAnswer,\n          selectedAnswer: participant.selectedOption\n        });\n      }\n    } catch (err) {\n      setError('Quiz not found or inactive');\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n  useEffect(() => {\n    fetchQuiz();\n\n    // Join quiz room\n    socket.emit('join-quiz', id);\n\n    // Listen for real-time updates\n    socket.on('quiz-update', data => {\n      setQuiz(data.quiz);\n    });\n    socket.on('results-visibility-changed', data => {\n      setQuiz(data.quiz);\n    });\n    socket.on('answer-feedback', data => {\n      setFeedback(data);\n      setHasAnswered(true);\n    });\n    socket.on('error', data => {\n      setError(data.message);\n    });\n    return () => {\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n      socket.off('answer-feedback');\n      socket.off('error');\n    };\n  }, [id, fetchQuiz]);\n  const handleAnswer = optionIndex => {\n    if (hasAnswered || !quiz.isActive) return;\n    setSelectedOption(optionIndex);\n\n    // Emit answer via socket\n    socket.emit('answer', {\n      quizId: id,\n      optionIndex,\n      socketId: socket.id\n    });\n  };\n  const getTotalAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options.reduce((total, option) => total + option.selectedCount, 0);\n  };\n  const getPercentage = selectedCount => {\n    const total = getTotalAnswers();\n    return total === 0 ? 0 : Math.round(selectedCount / total * 100);\n  };\n  const getCorrectAnswers = () => {\n    var _quiz$options$quiz$co;\n    if (!quiz) return 0;\n    return ((_quiz$options$quiz$co = quiz.options[quiz.correctAnswer]) === null || _quiz$options$quiz$co === void 0 ? void 0 : _quiz$options$quiz$co.selectedCount) || 0;\n  };\n  const getSuccessRate = () => {\n    const total = getTotalAnswers();\n    const correct = getCorrectAnswers();\n    return total > 0 ? Math.round(correct / total * 100) : 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading quiz...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 12\n    }, this);\n  }\n  if (!quiz) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: \"Quiz not found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"session-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-type\",\n        children: \"\\uD83E\\uDDE0 Live Quiz\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `session-status ${quiz.isActive ? 'active' : 'ended'}`,\n        children: quiz.isActive ? 'Active' : 'Ended'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"question-text\",\n        children: quiz.question\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), !hasAnswered && quiz.isActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"instruction\",\n          children: \"Choose your answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"options-list\",\n          children: quiz.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"option-item\",\n            onClick: () => handleAnswer(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-letter\",\n              children: String.fromCharCode(65 + index)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-text\",\n              children: option.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feedback-container\",\n        children: hasAnswered && feedback ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-icon\",\n            children: feedback.isCorrect ? '🎉' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-text\",\n            children: feedback.isCorrect ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Correct!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Great job! You got it right.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Incorrect.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"The correct answer was: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: (_quiz$options$feedbac = quiz.options[feedback.correctAnswer]) === null || _quiz$options$feedbac === void 0 ? void 0 : _quiz$options$feedbac.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 50\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 15\n        }, this) : !quiz.isActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feedback info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-icon\",\n            children: \"\\u23F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-text\",\n            children: \"This quiz has ended. No more answers are being accepted.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this) : null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"results-title\",\n        children: [\"\\uD83D\\uDCCA Live Results (\", getTotalAnswers(), \" total answers)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: getCorrectAnswers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Correct Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: getTotalAnswers() - getCorrectAnswers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Incorrect Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: [getSuccessRate(), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Success Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-list\",\n        children: quiz.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-option\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-letter\",\n                children: String.fromCharCode(65 + index)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-text\",\n                children: [option.text, index === quiz.correctAnswer && ' ✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"result-count\",\n              children: [option.selectedCount, \" answers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `result-fill ${index === quiz.correctAnswer ? 'correct' : ''} ${selectedOption === index ? 'user-selected' : ''}`,\n              style: {\n                width: `${getPercentage(option.selectedCount)}%`\n              },\n              children: [getPercentage(option.selectedCount), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Session ID: \", quiz._id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Participants: \", ((_quiz$participants = quiz.participants) === null || _quiz$participants === void 0 ? void 0 : _quiz$participants.length) || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n}\n_s(QuizSession, \"L86uHG89nVL7wgoYkYagbPUe8wM=\", false, function () {\n  return [useParams];\n});\n_c = QuizSession;\nexport default QuizSession;\nvar _c;\n$RefreshReg$(_c, \"QuizSession\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "axios", "socket", "jsxDEV", "_jsxDEV", "QuizSession", "_s", "_quiz$options$feedbac", "_quiz$participants", "id", "quiz", "setQuiz", "selectedOption", "setSelectedOption", "hasAnswered", "setHasAnswered", "feedback", "setFeedback", "loading", "setLoading", "error", "setError", "fetchQuiz", "_response$data$partic", "response", "get", "data", "participant", "participants", "find", "p", "socketId", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "err", "emit", "on", "message", "off", "handleAnswer", "optionIndex", "isActive", "quizId", "getTotalAnswers", "options", "reduce", "total", "option", "selectedCount", "getPercentage", "Math", "round", "getCorrectAnswers", "_quiz$options$quiz$co", "getSuccessRate", "correct", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "question", "map", "index", "onClick", "String", "fromCharCode", "text", "style", "width", "_id", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/pages/QuizSession.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport socket from '../socket';\n\nfunction QuizSession() {\n  const { id } = useParams();\n  const [quiz, setQuiz] = useState(null);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [hasAnswered, setHasAnswered] = useState(false);\n  const [feedback, setFeedback] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  const fetchQuiz = useCallback(async () => {\n    try {\n      const response = await axios.get(`/api/quizzes/${id}`);\n      setQuiz(response.data);\n      \n      // Check if user already answered\n      const participant = response.data.participants?.find(p => p.socketId === socket.id);\n      if (participant && participant.hasAnswered) {\n        setHasAnswered(true);\n        setSelectedOption(participant.selectedOption);\n        setFeedback({\n          isCorrect: participant.isCorrect,\n          correctAnswer: response.data.correctAnswer,\n          selectedAnswer: participant.selectedOption\n        });\n      }\n    } catch (err) {\n      setError('Quiz not found or inactive');\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchQuiz();\n\n    // Join quiz room\n    socket.emit('join-quiz', id);\n\n    // Listen for real-time updates\n    socket.on('quiz-update', (data) => {\n      setQuiz(data.quiz);\n    });\n\n    socket.on('results-visibility-changed', (data) => {\n      setQuiz(data.quiz);\n    });\n\n    socket.on('answer-feedback', (data) => {\n      setFeedback(data);\n      setHasAnswered(true);\n    });\n\n    socket.on('error', (data) => {\n      setError(data.message);\n    });\n\n    return () => {\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n      socket.off('answer-feedback');\n      socket.off('error');\n    };\n  }, [id, fetchQuiz]);\n\n  const handleAnswer = (optionIndex) => {\n    if (hasAnswered || !quiz.isActive) return;\n    \n    setSelectedOption(optionIndex);\n    \n    // Emit answer via socket\n    socket.emit('answer', {\n      quizId: id,\n      optionIndex,\n      socketId: socket.id\n    });\n  };\n\n  const getTotalAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options.reduce((total, option) => total + option.selectedCount, 0);\n  };\n\n  const getPercentage = (selectedCount) => {\n    const total = getTotalAnswers();\n    return total === 0 ? 0 : Math.round((selectedCount / total) * 100);\n  };\n\n  const getCorrectAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options[quiz.correctAnswer]?.selectedCount || 0;\n  };\n\n  const getSuccessRate = () => {\n    const total = getTotalAnswers();\n    const correct = getCorrectAnswers();\n    return total > 0 ? Math.round((correct / total) * 100) : 0;\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading quiz...</div>;\n  }\n\n  if (error) {\n    return <div className=\"error\">{error}</div>;\n  }\n\n  if (!quiz) {\n    return <div className=\"error\">Quiz not found</div>;\n  }\n\n  return (\n    <div className=\"session-container\">\n      <div className=\"session-header\">\n        <div className=\"session-type\">🧠 Live Quiz</div>\n        <div className={`session-status ${quiz.isActive ? 'active' : 'ended'}`}>\n          {quiz.isActive ? 'Active' : 'Ended'}\n        </div>\n      </div>\n\n      <div className=\"question-card\">\n        <h2 className=\"question-text\">{quiz.question}</h2>\n        \n        {!hasAnswered && quiz.isActive ? (\n          <div className=\"options-container\">\n            <p className=\"instruction\">Choose your answer:</p>\n            <ul className=\"options-list\">\n              {quiz.options.map((option, index) => (\n                <li \n                  key={index} \n                  className=\"option-item\"\n                  onClick={() => handleAnswer(index)}\n                >\n                  <div className=\"option-letter\">\n                    {String.fromCharCode(65 + index)}\n                  </div>\n                  <div className=\"option-text\">{option.text}</div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        ) : (\n          <div className=\"feedback-container\">\n            {hasAnswered && feedback ? (\n              <div className={`feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`}>\n                <div className=\"feedback-icon\">\n                  {feedback.isCorrect ? '🎉' : '❌'}\n                </div>\n                <div className=\"feedback-text\">\n                  {feedback.isCorrect ? (\n                    <div>\n                      <strong>Correct!</strong>\n                      <p>Great job! You got it right.</p>\n                    </div>\n                  ) : (\n                    <div>\n                      <strong>Incorrect.</strong>\n                      <p>The correct answer was: <strong>{quiz.options[feedback.correctAnswer]?.text}</strong></p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ) : !quiz.isActive ? (\n              <div className=\"feedback info\">\n                <div className=\"feedback-icon\">⏰</div>\n                <div className=\"feedback-text\">\n                  This quiz has ended. No more answers are being accepted.\n                </div>\n              </div>\n            ) : null}\n          </div>\n        )}\n      </div>\n\n      {/* Always show results for real-time experience */}\n      <div className=\"results-card\">\n        <h3 className=\"results-title\">\n          📊 Live Results ({getTotalAnswers()} total answers)\n        </h3>\n\n        <div className=\"quiz-stats\">\n          <div className=\"stat-item\">\n            <span className=\"stat-value\">{getCorrectAnswers()}</span>\n            <span className=\"stat-label\">Correct Answers</span>\n          </div>\n          <div className=\"stat-item\">\n            <span className=\"stat-value\">{getTotalAnswers() - getCorrectAnswers()}</span>\n            <span className=\"stat-label\">Incorrect Answers</span>\n          </div>\n          <div className=\"stat-item\">\n            <span className=\"stat-value\">{getSuccessRate()}%</span>\n            <span className=\"stat-label\">Success Rate</span>\n          </div>\n        </div>\n\n        <div className=\"results-list\">\n          {quiz.options.map((option, index) => (\n            <div key={index} className=\"result-item\">\n              <div className=\"result-header\">\n                <div className=\"result-option\">\n                  <div className=\"option-letter\">\n                    {String.fromCharCode(65 + index)}\n                  </div>\n                  <span className=\"option-text\">\n                    {option.text}\n                    {index === quiz.correctAnswer && ' ✅'}\n                  </span>\n                </div>\n                <span className=\"result-count\">{option.selectedCount} answers</span>\n              </div>\n              <div className=\"result-bar\">\n                <div\n                  className={`result-fill ${\n                    index === quiz.correctAnswer ? 'correct' : ''\n                  } ${selectedOption === index ? 'user-selected' : ''}`}\n                  style={{ width: `${getPercentage(option.selectedCount)}%` }}\n                >\n                  {getPercentage(option.selectedCount)}%\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"session-info\">\n        <p>Session ID: {quiz._id}</p>\n        <p>Participants: {quiz.participants?.length || 0}</p>\n      </div>\n    </div>\n  );\n}\n\nexport default QuizSession;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMyB,SAAS,GAAGvB,WAAW,CAAC,YAAY;IACxC,IAAI;MAAA,IAAAwB,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,gBAAgBhB,EAAE,EAAE,CAAC;MACtDE,OAAO,CAACa,QAAQ,CAACE,IAAI,CAAC;;MAEtB;MACA,MAAMC,WAAW,IAAAJ,qBAAA,GAAGC,QAAQ,CAACE,IAAI,CAACE,YAAY,cAAAL,qBAAA,uBAA1BA,qBAAA,CAA4BM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK7B,MAAM,CAACO,EAAE,CAAC;MACnF,IAAIkB,WAAW,IAAIA,WAAW,CAACb,WAAW,EAAE;QAC1CC,cAAc,CAAC,IAAI,CAAC;QACpBF,iBAAiB,CAACc,WAAW,CAACf,cAAc,CAAC;QAC7CK,WAAW,CAAC;UACVe,SAAS,EAAEL,WAAW,CAACK,SAAS;UAChCC,aAAa,EAAET,QAAQ,CAACE,IAAI,CAACO,aAAa;UAC1CC,cAAc,EAAEP,WAAW,CAACf;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZd,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;EAERX,SAAS,CAAC,MAAM;IACdwB,SAAS,CAAC,CAAC;;IAEX;IACApB,MAAM,CAACkC,IAAI,CAAC,WAAW,EAAE3B,EAAE,CAAC;;IAE5B;IACAP,MAAM,CAACmC,EAAE,CAAC,aAAa,EAAGX,IAAI,IAAK;MACjCf,OAAO,CAACe,IAAI,CAAChB,IAAI,CAAC;IACpB,CAAC,CAAC;IAEFR,MAAM,CAACmC,EAAE,CAAC,4BAA4B,EAAGX,IAAI,IAAK;MAChDf,OAAO,CAACe,IAAI,CAAChB,IAAI,CAAC;IACpB,CAAC,CAAC;IAEFR,MAAM,CAACmC,EAAE,CAAC,iBAAiB,EAAGX,IAAI,IAAK;MACrCT,WAAW,CAACS,IAAI,CAAC;MACjBX,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC;IAEFb,MAAM,CAACmC,EAAE,CAAC,OAAO,EAAGX,IAAI,IAAK;MAC3BL,QAAQ,CAACK,IAAI,CAACY,OAAO,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,MAAM;MACXpC,MAAM,CAACqC,GAAG,CAAC,aAAa,CAAC;MACzBrC,MAAM,CAACqC,GAAG,CAAC,4BAA4B,CAAC;MACxCrC,MAAM,CAACqC,GAAG,CAAC,iBAAiB,CAAC;MAC7BrC,MAAM,CAACqC,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAAC9B,EAAE,EAAEa,SAAS,CAAC,CAAC;EAEnB,MAAMkB,YAAY,GAAIC,WAAW,IAAK;IACpC,IAAI3B,WAAW,IAAI,CAACJ,IAAI,CAACgC,QAAQ,EAAE;IAEnC7B,iBAAiB,CAAC4B,WAAW,CAAC;;IAE9B;IACAvC,MAAM,CAACkC,IAAI,CAAC,QAAQ,EAAE;MACpBO,MAAM,EAAElC,EAAE;MACVgC,WAAW;MACXV,QAAQ,EAAE7B,MAAM,CAACO;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAClC,IAAI,EAAE,OAAO,CAAC;IACnB,OAAOA,IAAI,CAACmC,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACC,aAAa,EAAE,CAAC,CAAC;EAChF,CAAC;EAED,MAAMC,aAAa,GAAID,aAAa,IAAK;IACvC,MAAMF,KAAK,GAAGH,eAAe,CAAC,CAAC;IAC/B,OAAOG,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAEH,aAAa,GAAGF,KAAK,GAAI,GAAG,CAAC;EACpE,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC9B,IAAI,CAAC5C,IAAI,EAAE,OAAO,CAAC;IACnB,OAAO,EAAA4C,qBAAA,GAAA5C,IAAI,CAACmC,OAAO,CAACnC,IAAI,CAACuB,aAAa,CAAC,cAAAqB,qBAAA,uBAAhCA,qBAAA,CAAkCL,aAAa,KAAI,CAAC;EAC7D,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMR,KAAK,GAAGH,eAAe,CAAC,CAAC;IAC/B,MAAMY,OAAO,GAAGH,iBAAiB,CAAC,CAAC;IACnC,OAAON,KAAK,GAAG,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAEI,OAAO,GAAGT,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;EAC5D,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBAAOd,OAAA;MAAKqD,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvD;EAEA,IAAI1C,KAAK,EAAE;IACT,oBAAOhB,OAAA;MAAKqD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEtC;IAAK;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7C;EAEA,IAAI,CAACpD,IAAI,EAAE;IACT,oBAAON,OAAA;MAAKqD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpD;EAEA,oBACE1D,OAAA;IAAKqD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCtD,OAAA;MAAKqD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtD,OAAA;QAAKqD,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChD1D,OAAA;QAAKqD,SAAS,EAAE,kBAAkB/C,IAAI,CAACgC,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAG;QAAAgB,QAAA,EACpEhD,IAAI,CAACgC,QAAQ,GAAG,QAAQ,GAAG;MAAO;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1D,OAAA;MAAKqD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BtD,OAAA;QAAIqD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEhD,IAAI,CAACqD;MAAQ;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEjD,CAAChD,WAAW,IAAIJ,IAAI,CAACgC,QAAQ,gBAC5BtC,OAAA;QAAKqD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtD,OAAA;UAAGqD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClD1D,OAAA;UAAIqD,SAAS,EAAC,cAAc;UAAAC,QAAA,EACzBhD,IAAI,CAACmC,OAAO,CAACmB,GAAG,CAAC,CAAChB,MAAM,EAAEiB,KAAK,kBAC9B7D,OAAA;YAEEqD,SAAS,EAAC,aAAa;YACvBS,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACyB,KAAK,CAAE;YAAAP,QAAA,gBAEnCtD,OAAA;cAAKqD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BS,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEV,MAAM,CAACqB;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP3CG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQR,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEN1D,OAAA;QAAKqD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChC5C,WAAW,IAAIE,QAAQ,gBACtBZ,OAAA;UAAKqD,SAAS,EAAE,YAAYzC,QAAQ,CAACgB,SAAS,GAAG,SAAS,GAAG,WAAW,EAAG;UAAA0B,QAAA,gBACzEtD,OAAA;YAAKqD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B1C,QAAQ,CAACgB,SAAS,GAAG,IAAI,GAAG;UAAG;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B1C,QAAQ,CAACgB,SAAS,gBACjB5B,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzB1D,OAAA;gBAAAsD,QAAA,EAAG;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,gBAEN1D,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3B1D,OAAA;gBAAAsD,QAAA,GAAG,0BAAwB,eAAAtD,OAAA;kBAAAsD,QAAA,GAAAnD,qBAAA,GAASG,IAAI,CAACmC,OAAO,CAAC7B,QAAQ,CAACiB,aAAa,CAAC,cAAA1B,qBAAA,uBAApCA,qBAAA,CAAsC8D;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ,CAACpD,IAAI,CAACgC,QAAQ,gBAChBtC,OAAA;UAAKqD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtD,OAAA;YAAKqD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtC1D,OAAA;YAAKqD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BtD,OAAA;QAAIqD,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,6BACX,EAACd,eAAe,CAAC,CAAC,EAAC,iBACtC;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL1D,OAAA;QAAKqD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtD,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtD,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEL,iBAAiB,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD1D,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN1D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtD,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,eAAe,CAAC,CAAC,GAAGS,iBAAiB,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7E1D,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN1D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtD,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEH,cAAc,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvD1D,OAAA;YAAMqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKqD,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BhD,IAAI,CAACmC,OAAO,CAACmB,GAAG,CAAC,CAAChB,MAAM,EAAEiB,KAAK,kBAC9B7D,OAAA;UAAiBqD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtCtD,OAAA;YAAKqD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtD,OAAA;cAAKqD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BS,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACN1D,OAAA;gBAAMqD,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAC1BV,MAAM,CAACqB,IAAI,EACXJ,KAAK,KAAKvD,IAAI,CAACuB,aAAa,IAAI,IAAI;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1D,OAAA;cAAMqD,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAEV,MAAM,CAACC,aAAa,EAAC,UAAQ;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBtD,OAAA;cACEqD,SAAS,EAAE,eACTQ,KAAK,KAAKvD,IAAI,CAACuB,aAAa,GAAG,SAAS,GAAG,EAAE,IAC3CrB,cAAc,KAAKqD,KAAK,GAAG,eAAe,GAAG,EAAE,EAAG;cACtDK,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGrB,aAAa,CAACF,MAAM,CAACC,aAAa,CAAC;cAAI,CAAE;cAAAS,QAAA,GAE3DR,aAAa,CAACF,MAAM,CAACC,aAAa,CAAC,EAAC,GACvC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAtBEG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1D,OAAA;MAAKqD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BtD,OAAA;QAAAsD,QAAA,GAAG,cAAY,EAAChD,IAAI,CAAC8D,GAAG;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B1D,OAAA;QAAAsD,QAAA,GAAG,gBAAc,EAAC,EAAAlD,kBAAA,GAAAE,IAAI,CAACkB,YAAY,cAAApB,kBAAA,uBAAjBA,kBAAA,CAAmBiE,MAAM,KAAI,CAAC;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxD,EAAA,CAtOQD,WAAW;EAAA,QACHL,SAAS;AAAA;AAAA0E,EAAA,GADjBrE,WAAW;AAwOpB,eAAeA,WAAW;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}