{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lpqa cpy\\\\frontend\\\\src\\\\pages\\\\ControlCenter.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport api from '../api';\nimport socket from '../socket';\n// QR Code removed as requested\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ControlCenter() {\n  _s();\n  const [polls, setPolls] = useState([]);\n  const [quizzes, setQuizzes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  // selectedSession removed with QR code functionality\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchSessions();\n\n    // Listen for real-time updates\n    socket.on('poll-update', data => {\n      setPolls(prev => prev.map(poll => poll._id === data.poll._id ? data.poll : poll));\n    });\n    socket.on('quiz-update', data => {\n      setQuizzes(prev => prev.map(quiz => quiz._id === data.quiz._id ? data.quiz : quiz));\n    });\n    socket.on('results-visibility-changed', data => {\n      if (data.poll) {\n        setPolls(prev => prev.map(poll => poll._id === data.poll._id ? data.poll : poll));\n      }\n      if (data.quiz) {\n        setQuizzes(prev => prev.map(quiz => quiz._id === data.quiz._id ? data.quiz : quiz));\n      }\n    });\n    return () => {\n      socket.off('poll-update');\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n    };\n  }, []);\n  const fetchSessions = async () => {\n    try {\n      console.log('📡 Fetching sessions...');\n      const [pollsResponse, quizzesResponse] = await Promise.all([api.get('/polls'), api.get('/quizzes')]);\n      console.log('✅ Polls fetched:', pollsResponse.data);\n      console.log('✅ Quizzes fetched:', quizzesResponse.data);\n      setPolls(pollsResponse.data);\n      setQuizzes(quizzesResponse.data);\n      setError(''); // Clear any previous errors\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('❌ Fetch sessions error:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message || 'Unknown error';\n      setError(`Failed to fetch sessions: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleResults = async (type, id) => {\n    try {\n      console.log(`🔄 Toggling results for ${type} ${id}`);\n      const response = await api.patch(`/${type}s/${id}/toggle-results`);\n      console.log('✅ Toggle results response:', response.data);\n\n      // Update local state immediately\n      if (type === 'poll') {\n        setPolls(prev => prev.map(poll => poll._id === id ? {\n          ...poll,\n          showResults: response.data.showResults\n        } : poll));\n      } else {\n        setQuizzes(prev => prev.map(quiz => quiz._id === id ? {\n          ...quiz,\n          showResults: response.data.showResults\n        } : quiz));\n      }\n\n      // Emit socket event for real-time updates\n      socket.emit('toggle-results', {\n        type,\n        id\n      });\n    } catch (err) {\n      console.error('❌ Toggle results error:', err);\n      setError(`Failed to toggle results for ${type}`);\n    }\n  };\n  const endSession = async (type, id) => {\n    if (window.confirm(`Are you sure you want to end this ${type}?`)) {\n      try {\n        console.log(`🔄 Ending ${type} with ID:`, id);\n        const response = await api.patch(`/${type}s/${id}/end`);\n        console.log('✅ End session response:', response.data);\n        fetchSessions(); // Refresh the list\n        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} ended successfully!`);\n      } catch (err) {\n        var _err$response2, _err$response2$data;\n        console.error('❌ End session error:', err);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message || 'Unknown error';\n        setError(`Failed to end ${type}: ${errorMessage}`);\n        alert(`Failed to end ${type}: ${errorMessage}`);\n      }\n    }\n  };\n  const deleteSession = async (type, id) => {\n    if (window.confirm(`Are you sure you want to permanently delete this ${type}? This action cannot be undone.`)) {\n      try {\n        console.log(`🗑️ Deleting ${type} with ID:`, id);\n        const response = await api.delete(`/${type}s/${id}`);\n        console.log('✅ Delete session response:', response.data);\n        fetchSessions(); // Refresh the list\n        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`);\n      } catch (err) {\n        var _err$response3, _err$response3$data;\n        console.error('❌ Delete session error:', err);\n        const errorMessage = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message || 'Unknown error';\n        setError(`Failed to delete ${type}: ${errorMessage}`);\n        alert(`Failed to delete ${type}: ${errorMessage}`);\n      }\n    }\n  };\n  const joinAsParticipant = (type, id) => {\n    const url = `/${type}/${id}`;\n    window.open(url, '_blank');\n  };\n  const getTotalVotes = (session, type) => {\n    if (type === 'poll') {\n      return session.options.reduce((total, option) => total + option.voteCount, 0);\n    } else {\n      return session.options.reduce((total, option) => total + option.selectedCount, 0);\n    }\n  };\n  const getCorrectAnswers = quiz => {\n    var _quiz$options$quiz$co;\n    return ((_quiz$options$quiz$co = quiz.options[quiz.correctAnswer]) === null || _quiz$options$quiz$co === void 0 ? void 0 : _quiz$options$quiz$co.selectedCount) || 0;\n  };\n  const getSuccessRate = quiz => {\n    const total = getTotalVotes(quiz, 'quiz');\n    const correct = getCorrectAnswers(quiz);\n    return total > 0 ? Math.round(correct / total * 100) : 0;\n  };\n  const SessionCard = ({\n    session,\n    type\n  }) => {\n    var _session$participants;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"session-type-badge\",\n          children: [type === 'poll' ? '📊' : '🧠', \" \", type.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `session-status ${session.isActive ? 'active' : 'ended'}`,\n          children: session.isActive ? 'Active' : 'Ended'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"session-question\",\n          children: session.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"session-code-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"session-code-label\",\n            children: \"Session Code:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"session-code-value\",\n            children: session.sessionCode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"session-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: getTotalVotes(session, type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: type === 'poll' ? 'Votes' : 'Answers'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: ((_session$participants = session.participants) === null || _session$participants === void 0 ? void 0 : _session$participants.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Participants\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 11\n          }, this), type === 'quiz' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: [getSuccessRate(session), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Success Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"session-options\",\n          children: session.options.map((option, index) => {\n            const count = type === 'poll' ? option.voteCount : option.selectedCount;\n            const total = getTotalVotes(session, type);\n            const percentage = total > 0 ? Math.round(count / total * 100) : 0;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-result\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"option-text\",\n                  children: [String.fromCharCode(65 + index), \". \", option.text, type === 'quiz' && index === session.correctAnswer && ' ✅']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"option-count\",\n                  children: count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-bar\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `option-fill ${type === 'quiz' && index === session.correctAnswer ? 'correct' : ''}`,\n                  style: {\n                    width: `${percentage}%`\n                  },\n                  children: [percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => joinAsParticipant(type, session._id),\n          children: \"Join as Participant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 9\n        }, this), session.isActive && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `btn btn-sm ${session.showResults ? 'btn-warning' : 'btn-success'}`,\n          onClick: () => toggleResults(type, session._id),\n          children: session.showResults ? 'Hide Results' : 'Show Results'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), session.isActive && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger btn-sm\",\n          onClick: () => endSession(type, session._id),\n          children: \"End Session\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary btn-sm\",\n          onClick: () => {\n            const url = `${window.location.origin}/${type}/${session._id}`;\n            navigator.clipboard.writeText(url);\n            alert('Session URL copied to clipboard!');\n          },\n          children: \"Copy Share URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger btn-sm\",\n          onClick: () => deleteSession(type, session._id),\n          title: `Delete this ${type} permanently`,\n          children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 5\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading sessions...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"control-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"dashboard-title\",\n        children: \"Control Center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"dashboard-subtitle\",\n        children: \"Monitor and manage all your live polls and quizzes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => navigate('/admin'),\n        children: \"Create New Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sessions-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sessions-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83D\\uDCCA Live Polls (\", polls.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), polls.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No polls created yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => navigate('/admin'),\n            children: \"Create Your First Poll\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sessions-list\",\n          children: polls.map(poll => /*#__PURE__*/_jsxDEV(SessionCard, {\n            session: poll,\n            type: \"poll\"\n          }, poll._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sessions-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83E\\uDDE0 Live Quizzes (\", quizzes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), quizzes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No quizzes created yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => navigate('/admin'),\n            children: \"Create Your First Quiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sessions-list\",\n          children: quizzes.map(quiz => /*#__PURE__*/_jsxDEV(SessionCard, {\n            session: quiz,\n            type: \"quiz\"\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n}\n_s(ControlCenter, \"H4benl+BMKkowlnWrQfxadgrWvY=\", false, function () {\n  return [useNavigate];\n});\n_c = ControlCenter;\nexport default ControlCenter;\nvar _c;\n$RefreshReg$(_c, \"ControlCenter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "api", "socket", "jsxDEV", "_jsxDEV", "ControlCenter", "_s", "polls", "setPolls", "quizzes", "setQuizzes", "loading", "setLoading", "error", "setError", "navigate", "fetchSessions", "on", "data", "prev", "map", "poll", "_id", "quiz", "off", "console", "log", "pollsResponse", "quizzesResponse", "Promise", "all", "get", "err", "_err$response", "_err$response$data", "errorMessage", "response", "message", "toggleResults", "type", "id", "patch", "showResults", "emit", "endSession", "window", "confirm", "alert", "char<PERSON>t", "toUpperCase", "slice", "_err$response2", "_err$response2$data", "deleteSession", "delete", "_err$response3", "_err$response3$data", "joinAsParticipant", "url", "open", "getTotalVotes", "session", "options", "reduce", "total", "option", "voteCount", "selectedCount", "getCorrectAnswers", "_quiz$options$quiz$co", "<PERSON><PERSON><PERSON><PERSON>", "getSuccessRate", "correct", "Math", "round", "SessionCard", "_session$participants", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isActive", "question", "sessionCode", "participants", "length", "index", "count", "percentage", "String", "fromCharCode", "text", "style", "width", "onClick", "location", "origin", "navigator", "clipboard", "writeText", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/pages/ControlCenter.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport api from '../api';\nimport socket from '../socket';\n// QR Code removed as requested\n\nfunction ControlCenter() {\n  const [polls, setPolls] = useState([]);\n  const [quizzes, setQuizzes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  // selectedSession removed with QR code functionality\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    fetchSessions();\n    \n    // Listen for real-time updates\n    socket.on('poll-update', (data) => {\n      setPolls(prev => prev.map(poll => \n        poll._id === data.poll._id ? data.poll : poll\n      ));\n    });\n\n    socket.on('quiz-update', (data) => {\n      setQuizzes(prev => prev.map(quiz => \n        quiz._id === data.quiz._id ? data.quiz : quiz\n      ));\n    });\n\n    socket.on('results-visibility-changed', (data) => {\n      if (data.poll) {\n        setPolls(prev => prev.map(poll => \n          poll._id === data.poll._id ? data.poll : poll\n        ));\n      }\n      if (data.quiz) {\n        setQuizzes(prev => prev.map(quiz => \n          quiz._id === data.quiz._id ? data.quiz : quiz\n        ));\n      }\n    });\n\n    return () => {\n      socket.off('poll-update');\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n    };\n  }, []);\n\n  const fetchSessions = async () => {\n    try {\n      console.log('📡 Fetching sessions...');\n      const [pollsResponse, quizzesResponse] = await Promise.all([\n        api.get('/polls'),\n        api.get('/quizzes')\n      ]);\n\n      console.log('✅ Polls fetched:', pollsResponse.data);\n      console.log('✅ Quizzes fetched:', quizzesResponse.data);\n\n      setPolls(pollsResponse.data);\n      setQuizzes(quizzesResponse.data);\n      setError(''); // Clear any previous errors\n    } catch (err) {\n      console.error('❌ Fetch sessions error:', err);\n      const errorMessage = err.response?.data?.error || err.message || 'Unknown error';\n      setError(`Failed to fetch sessions: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const toggleResults = async (type, id) => {\n    try {\n      console.log(`🔄 Toggling results for ${type} ${id}`);\n      const response = await api.patch(`/${type}s/${id}/toggle-results`);\n      console.log('✅ Toggle results response:', response.data);\n\n      // Update local state immediately\n      if (type === 'poll') {\n        setPolls(prev => prev.map(poll =>\n          poll._id === id ? { ...poll, showResults: response.data.showResults } : poll\n        ));\n      } else {\n        setQuizzes(prev => prev.map(quiz =>\n          quiz._id === id ? { ...quiz, showResults: response.data.showResults } : quiz\n        ));\n      }\n\n      // Emit socket event for real-time updates\n      socket.emit('toggle-results', { type, id });\n    } catch (err) {\n      console.error('❌ Toggle results error:', err);\n      setError(`Failed to toggle results for ${type}`);\n    }\n  };\n\n  const endSession = async (type, id) => {\n    if (window.confirm(`Are you sure you want to end this ${type}?`)) {\n      try {\n        console.log(`🔄 Ending ${type} with ID:`, id);\n        const response = await api.patch(`/${type}s/${id}/end`);\n        console.log('✅ End session response:', response.data);\n        fetchSessions(); // Refresh the list\n        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} ended successfully!`);\n      } catch (err) {\n        console.error('❌ End session error:', err);\n        const errorMessage = err.response?.data?.error || err.message || 'Unknown error';\n        setError(`Failed to end ${type}: ${errorMessage}`);\n        alert(`Failed to end ${type}: ${errorMessage}`);\n      }\n    }\n  };\n\n  const deleteSession = async (type, id) => {\n    if (window.confirm(`Are you sure you want to permanently delete this ${type}? This action cannot be undone.`)) {\n      try {\n        console.log(`🗑️ Deleting ${type} with ID:`, id);\n        const response = await api.delete(`/${type}s/${id}`);\n        console.log('✅ Delete session response:', response.data);\n        fetchSessions(); // Refresh the list\n        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`);\n      } catch (err) {\n        console.error('❌ Delete session error:', err);\n        const errorMessage = err.response?.data?.error || err.message || 'Unknown error';\n        setError(`Failed to delete ${type}: ${errorMessage}`);\n        alert(`Failed to delete ${type}: ${errorMessage}`);\n      }\n    }\n  };\n\n  const joinAsParticipant = (type, id) => {\n    const url = `/${type}/${id}`;\n    window.open(url, '_blank');\n  };\n\n  const getTotalVotes = (session, type) => {\n    if (type === 'poll') {\n      return session.options.reduce((total, option) => total + option.voteCount, 0);\n    } else {\n      return session.options.reduce((total, option) => total + option.selectedCount, 0);\n    }\n  };\n\n  const getCorrectAnswers = (quiz) => {\n    return quiz.options[quiz.correctAnswer]?.selectedCount || 0;\n  };\n\n  const getSuccessRate = (quiz) => {\n    const total = getTotalVotes(quiz, 'quiz');\n    const correct = getCorrectAnswers(quiz);\n    return total > 0 ? Math.round((correct / total) * 100) : 0;\n  };\n\n  const SessionCard = ({ session, type }) => (\n    <div className=\"session-card\">\n      <div className=\"session-header\">\n        <div className=\"session-type-badge\">\n          {type === 'poll' ? '📊' : '🧠'} {type.toUpperCase()}\n        </div>\n        <div className={`session-status ${session.isActive ? 'active' : 'ended'}`}>\n          {session.isActive ? 'Active' : 'Ended'}\n        </div>\n      </div>\n\n      <div className=\"session-content\">\n        <h3 className=\"session-question\">{session.question}</h3>\n        <div className=\"session-code-info\">\n          <span className=\"session-code-label\">Session Code:</span>\n          <span className=\"session-code-value\">{session.sessionCode}</span>\n        </div>\n        <div className=\"session-stats\">\n          <div className=\"stat\">\n            <span className=\"stat-value\">{getTotalVotes(session, type)}</span>\n            <span className=\"stat-label\">{type === 'poll' ? 'Votes' : 'Answers'}</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{session.participants?.length || 0}</span>\n            <span className=\"stat-label\">Participants</span>\n          </div>\n          {type === 'quiz' && (\n            <div className=\"stat\">\n              <span className=\"stat-value\">{getSuccessRate(session)}%</span>\n              <span className=\"stat-label\">Success Rate</span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"session-options\">\n          {session.options.map((option, index) => {\n            const count = type === 'poll' ? option.voteCount : option.selectedCount;\n            const total = getTotalVotes(session, type);\n            const percentage = total > 0 ? Math.round((count / total) * 100) : 0;\n            \n            return (\n              <div key={index} className=\"option-result\">\n                <div className=\"option-header\">\n                  <span className=\"option-text\">\n                    {String.fromCharCode(65 + index)}. {option.text}\n                    {type === 'quiz' && index === session.correctAnswer && ' ✅'}\n                  </span>\n                  <span className=\"option-count\">{count}</span>\n                </div>\n                <div className=\"option-bar\">\n                  <div \n                    className={`option-fill ${type === 'quiz' && index === session.correctAnswer ? 'correct' : ''}`}\n                    style={{ width: `${percentage}%` }}\n                  >\n                    {percentage}%\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      <div className=\"session-actions\">\n        <button \n          className=\"btn btn-primary btn-sm\"\n          onClick={() => joinAsParticipant(type, session._id)}\n        >\n          Join as Participant\n        </button>\n        \n        {session.isActive && (\n          <button \n            className={`btn btn-sm ${session.showResults ? 'btn-warning' : 'btn-success'}`}\n            onClick={() => toggleResults(type, session._id)}\n          >\n            {session.showResults ? 'Hide Results' : 'Show Results'}\n          </button>\n        )}\n        \n        {session.isActive && (\n          <button \n            className=\"btn btn-danger btn-sm\"\n            onClick={() => endSession(type, session._id)}\n          >\n            End Session\n          </button>\n        )}\n\n        <button\n          className=\"btn btn-secondary btn-sm\"\n          onClick={() => {\n            const url = `${window.location.origin}/${type}/${session._id}`;\n            navigator.clipboard.writeText(url);\n            alert('Session URL copied to clipboard!');\n          }}\n        >\n          Copy Share URL\n        </button>\n\n        <button\n          className=\"btn btn-danger btn-sm\"\n          onClick={() => deleteSession(type, session._id)}\n          title={`Delete this ${type} permanently`}\n        >\n          🗑️ Delete\n        </button>\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return <div className=\"loading\">Loading sessions...</div>;\n  }\n\n  return (\n    <div className=\"control-center\">\n      <div className=\"dashboard-header\">\n        <h1 className=\"dashboard-title\">Control Center</h1>\n        <p className=\"dashboard-subtitle\">\n          Monitor and manage all your live polls and quizzes\n        </p>\n        <button \n          className=\"btn btn-primary\"\n          onClick={() => navigate('/admin')}\n        >\n          Create New Session\n        </button>\n      </div>\n\n      {error && <div className=\"error-message\">{error}</div>}\n\n      <div className=\"sessions-grid\">\n        <div className=\"sessions-section\">\n          <h2>📊 Live Polls ({polls.length})</h2>\n          {polls.length === 0 ? (\n            <div className=\"empty-state\">\n              <p>No polls created yet</p>\n              <button \n                className=\"btn btn-primary\"\n                onClick={() => navigate('/admin')}\n              >\n                Create Your First Poll\n              </button>\n            </div>\n          ) : (\n            <div className=\"sessions-list\">\n              {polls.map(poll => (\n                <SessionCard key={poll._id} session={poll} type=\"poll\" />\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"sessions-section\">\n          <h2>🧠 Live Quizzes ({quizzes.length})</h2>\n          {quizzes.length === 0 ? (\n            <div className=\"empty-state\">\n              <p>No quizzes created yet</p>\n              <button \n                className=\"btn btn-primary\"\n                onClick={() => navigate('/admin')}\n              >\n                Create Your First Quiz\n              </button>\n            </div>\n          ) : (\n            <div className=\"sessions-list\">\n              {quizzes.map(quiz => (\n                <SessionCard key={quiz._id} session={quiz} type=\"quiz\" />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Share URL Modal - QR Code removed */}\n    </div>\n  );\n}\n\nexport default ControlCenter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,GAAG,MAAM,QAAQ;AACxB,OAAOC,MAAM,MAAM,WAAW;AAC9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC;EACA,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACdiB,aAAa,CAAC,CAAC;;IAEf;IACAd,MAAM,CAACe,EAAE,CAAC,aAAa,EAAGC,IAAI,IAAK;MACjCV,QAAQ,CAACW,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACC,GAAG,KAAKJ,IAAI,CAACG,IAAI,CAACC,GAAG,GAAGJ,IAAI,CAACG,IAAI,GAAGA,IAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFnB,MAAM,CAACe,EAAE,CAAC,aAAa,EAAGC,IAAI,IAAK;MACjCR,UAAU,CAACS,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACG,IAAI,IAC9BA,IAAI,CAACD,GAAG,KAAKJ,IAAI,CAACK,IAAI,CAACD,GAAG,GAAGJ,IAAI,CAACK,IAAI,GAAGA,IAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFrB,MAAM,CAACe,EAAE,CAAC,4BAA4B,EAAGC,IAAI,IAAK;MAChD,IAAIA,IAAI,CAACG,IAAI,EAAE;QACbb,QAAQ,CAACW,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACC,GAAG,KAAKJ,IAAI,CAACG,IAAI,CAACC,GAAG,GAAGJ,IAAI,CAACG,IAAI,GAAGA,IAC3C,CAAC,CAAC;MACJ;MACA,IAAIH,IAAI,CAACK,IAAI,EAAE;QACbb,UAAU,CAACS,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACG,IAAI,IAC9BA,IAAI,CAACD,GAAG,KAAKJ,IAAI,CAACK,IAAI,CAACD,GAAG,GAAGJ,IAAI,CAACK,IAAI,GAAGA,IAC3C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXrB,MAAM,CAACsB,GAAG,CAAC,aAAa,CAAC;MACzBtB,MAAM,CAACsB,GAAG,CAAC,aAAa,CAAC;MACzBtB,MAAM,CAACsB,GAAG,CAAC,4BAA4B,CAAC;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMR,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,MAAM,CAACC,aAAa,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzD7B,GAAG,CAAC8B,GAAG,CAAC,QAAQ,CAAC,EACjB9B,GAAG,CAAC8B,GAAG,CAAC,UAAU,CAAC,CACpB,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,aAAa,CAACT,IAAI,CAAC;MACnDO,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,eAAe,CAACV,IAAI,CAAC;MAEvDV,QAAQ,CAACmB,aAAa,CAACT,IAAI,CAAC;MAC5BR,UAAU,CAACkB,eAAe,CAACV,IAAI,CAAC;MAChCJ,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZT,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEmB,GAAG,CAAC;MAC7C,MAAMG,YAAY,GAAG,EAAAF,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcf,IAAI,cAAAgB,kBAAA,uBAAlBA,kBAAA,CAAoBrB,KAAK,KAAImB,GAAG,CAACK,OAAO,IAAI,eAAe;MAChFvB,QAAQ,CAAC,6BAA6BqB,YAAY,EAAE,CAAC;IACvD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAG,MAAAA,CAAOC,IAAI,EAAEC,EAAE,KAAK;IACxC,IAAI;MACFf,OAAO,CAACC,GAAG,CAAC,2BAA2Ba,IAAI,IAAIC,EAAE,EAAE,CAAC;MACpD,MAAMJ,QAAQ,GAAG,MAAMnC,GAAG,CAACwC,KAAK,CAAC,IAAIF,IAAI,KAAKC,EAAE,iBAAiB,CAAC;MAClEf,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,QAAQ,CAAClB,IAAI,CAAC;;MAExD;MACA,IAAIqB,IAAI,KAAK,MAAM,EAAE;QACnB/B,QAAQ,CAACW,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACC,GAAG,KAAKkB,EAAE,GAAG;UAAE,GAAGnB,IAAI;UAAEqB,WAAW,EAAEN,QAAQ,CAAClB,IAAI,CAACwB;QAAY,CAAC,GAAGrB,IAC1E,CAAC,CAAC;MACJ,CAAC,MAAM;QACLX,UAAU,CAACS,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACG,IAAI,IAC9BA,IAAI,CAACD,GAAG,KAAKkB,EAAE,GAAG;UAAE,GAAGjB,IAAI;UAAEmB,WAAW,EAAEN,QAAQ,CAAClB,IAAI,CAACwB;QAAY,CAAC,GAAGnB,IAC1E,CAAC,CAAC;MACJ;;MAEA;MACArB,MAAM,CAACyC,IAAI,CAAC,gBAAgB,EAAE;QAAEJ,IAAI;QAAEC;MAAG,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZP,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEmB,GAAG,CAAC;MAC7ClB,QAAQ,CAAC,gCAAgCyB,IAAI,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAOL,IAAI,EAAEC,EAAE,KAAK;IACrC,IAAIK,MAAM,CAACC,OAAO,CAAC,qCAAqCP,IAAI,GAAG,CAAC,EAAE;MAChE,IAAI;QACFd,OAAO,CAACC,GAAG,CAAC,aAAaa,IAAI,WAAW,EAAEC,EAAE,CAAC;QAC7C,MAAMJ,QAAQ,GAAG,MAAMnC,GAAG,CAACwC,KAAK,CAAC,IAAIF,IAAI,KAAKC,EAAE,MAAM,CAAC;QACvDf,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,QAAQ,CAAClB,IAAI,CAAC;QACrDF,aAAa,CAAC,CAAC,CAAC,CAAC;QACjB+B,KAAK,CAAC,GAAGR,IAAI,CAACS,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,CAAC,sBAAsB,CAAC;MAC9E,CAAC,CAAC,OAAOlB,GAAG,EAAE;QAAA,IAAAmB,cAAA,EAAAC,mBAAA;QACZ3B,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEmB,GAAG,CAAC;QAC1C,MAAMG,YAAY,GAAG,EAAAgB,cAAA,GAAAnB,GAAG,CAACI,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoBvC,KAAK,KAAImB,GAAG,CAACK,OAAO,IAAI,eAAe;QAChFvB,QAAQ,CAAC,iBAAiByB,IAAI,KAAKJ,YAAY,EAAE,CAAC;QAClDY,KAAK,CAAC,iBAAiBR,IAAI,KAAKJ,YAAY,EAAE,CAAC;MACjD;IACF;EACF,CAAC;EAED,MAAMkB,aAAa,GAAG,MAAAA,CAAOd,IAAI,EAAEC,EAAE,KAAK;IACxC,IAAIK,MAAM,CAACC,OAAO,CAAC,oDAAoDP,IAAI,iCAAiC,CAAC,EAAE;MAC7G,IAAI;QACFd,OAAO,CAACC,GAAG,CAAC,gBAAgBa,IAAI,WAAW,EAAEC,EAAE,CAAC;QAChD,MAAMJ,QAAQ,GAAG,MAAMnC,GAAG,CAACqD,MAAM,CAAC,IAAIf,IAAI,KAAKC,EAAE,EAAE,CAAC;QACpDf,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,QAAQ,CAAClB,IAAI,CAAC;QACxDF,aAAa,CAAC,CAAC,CAAC,CAAC;QACjB+B,KAAK,CAAC,GAAGR,IAAI,CAACS,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,CAAC,wBAAwB,CAAC;MAChF,CAAC,CAAC,OAAOlB,GAAG,EAAE;QAAA,IAAAuB,cAAA,EAAAC,mBAAA;QACZ/B,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEmB,GAAG,CAAC;QAC7C,MAAMG,YAAY,GAAG,EAAAoB,cAAA,GAAAvB,GAAG,CAACI,QAAQ,cAAAmB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrC,IAAI,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoB3C,KAAK,KAAImB,GAAG,CAACK,OAAO,IAAI,eAAe;QAChFvB,QAAQ,CAAC,oBAAoByB,IAAI,KAAKJ,YAAY,EAAE,CAAC;QACrDY,KAAK,CAAC,oBAAoBR,IAAI,KAAKJ,YAAY,EAAE,CAAC;MACpD;IACF;EACF,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAClB,IAAI,EAAEC,EAAE,KAAK;IACtC,MAAMkB,GAAG,GAAG,IAAInB,IAAI,IAAIC,EAAE,EAAE;IAC5BK,MAAM,CAACc,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;EAED,MAAME,aAAa,GAAGA,CAACC,OAAO,EAAEtB,IAAI,KAAK;IACvC,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOsB,OAAO,CAACC,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACC,SAAS,EAAE,CAAC,CAAC;IAC/E,CAAC,MAAM;MACL,OAAOL,OAAO,CAACC,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACE,aAAa,EAAE,CAAC,CAAC;IACnF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAI7C,IAAI,IAAK;IAAA,IAAA8C,qBAAA;IAClC,OAAO,EAAAA,qBAAA,GAAA9C,IAAI,CAACuC,OAAO,CAACvC,IAAI,CAAC+C,aAAa,CAAC,cAAAD,qBAAA,uBAAhCA,qBAAA,CAAkCF,aAAa,KAAI,CAAC;EAC7D,CAAC;EAED,MAAMI,cAAc,GAAIhD,IAAI,IAAK;IAC/B,MAAMyC,KAAK,GAAGJ,aAAa,CAACrC,IAAI,EAAE,MAAM,CAAC;IACzC,MAAMiD,OAAO,GAAGJ,iBAAiB,CAAC7C,IAAI,CAAC;IACvC,OAAOyC,KAAK,GAAG,CAAC,GAAGS,IAAI,CAACC,KAAK,CAAEF,OAAO,GAAGR,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;EAC5D,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAC;IAAEd,OAAO;IAAEtB;EAAK,CAAC;IAAA,IAAAqC,qBAAA;IAAA,oBACpCxE,OAAA;MAAKyE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1E,OAAA;QAAKyE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1E,OAAA;UAAKyE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAChCvC,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI,EAAC,GAAC,EAACA,IAAI,CAACU,WAAW,CAAC,CAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAE,kBAAkBhB,OAAO,CAACsB,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAG;UAAAL,QAAA,EACvEjB,OAAO,CAACsB,QAAQ,GAAG,QAAQ,GAAG;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1E,OAAA;UAAIyE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEjB,OAAO,CAACuB;QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxD9E,OAAA;UAAKyE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1E,OAAA;YAAMyE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzD9E,OAAA;YAAMyE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEjB,OAAO,CAACwB;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1E,OAAA;YAAKyE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB1E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAElB,aAAa,CAACC,OAAO,EAAEtB,IAAI;YAAC;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClE9E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEvC,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;YAAS;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACN9E,OAAA;YAAKyE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB1E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAAF,qBAAA,GAAAf,OAAO,CAACyB,YAAY,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBW,MAAM,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvE9E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EACL3C,IAAI,KAAK,MAAM,iBACdnC,OAAA;YAAKyE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB1E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAEP,cAAc,CAACV,OAAO,CAAC,EAAC,GAAC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D9E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BjB,OAAO,CAACC,OAAO,CAAC1C,GAAG,CAAC,CAAC6C,MAAM,EAAEuB,KAAK,KAAK;YACtC,MAAMC,KAAK,GAAGlD,IAAI,KAAK,MAAM,GAAG0B,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,aAAa;YACvE,MAAMH,KAAK,GAAGJ,aAAa,CAACC,OAAO,EAAEtB,IAAI,CAAC;YAC1C,MAAMmD,UAAU,GAAG1B,KAAK,GAAG,CAAC,GAAGS,IAAI,CAACC,KAAK,CAAEe,KAAK,GAAGzB,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;YAEpE,oBACE5D,OAAA;cAAiByE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBACxC1E,OAAA;gBAAKyE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B1E,OAAA;kBAAMyE,SAAS,EAAC,aAAa;kBAAAC,QAAA,GAC1Ba,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGJ,KAAK,CAAC,EAAC,IAAE,EAACvB,MAAM,CAAC4B,IAAI,EAC9CtD,IAAI,KAAK,MAAM,IAAIiD,KAAK,KAAK3B,OAAO,CAACS,aAAa,IAAI,IAAI;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACP9E,OAAA;kBAAMyE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEW;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB1E,OAAA;kBACEyE,SAAS,EAAE,eAAetC,IAAI,KAAK,MAAM,IAAIiD,KAAK,KAAK3B,OAAO,CAACS,aAAa,GAAG,SAAS,GAAG,EAAE,EAAG;kBAChGwB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGL,UAAU;kBAAI,CAAE;kBAAAZ,QAAA,GAElCY,UAAU,EAAC,GACd;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAfEM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1E,OAAA;UACEyE,SAAS,EAAC,wBAAwB;UAClCmB,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAAClB,IAAI,EAAEsB,OAAO,CAACvC,GAAG,CAAE;UAAAwD,QAAA,EACrD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERrB,OAAO,CAACsB,QAAQ,iBACf/E,OAAA;UACEyE,SAAS,EAAE,cAAchB,OAAO,CAACnB,WAAW,GAAG,aAAa,GAAG,aAAa,EAAG;UAC/EsD,OAAO,EAAEA,CAAA,KAAM1D,aAAa,CAACC,IAAI,EAAEsB,OAAO,CAACvC,GAAG,CAAE;UAAAwD,QAAA,EAE/CjB,OAAO,CAACnB,WAAW,GAAG,cAAc,GAAG;QAAc;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACT,EAEArB,OAAO,CAACsB,QAAQ,iBACf/E,OAAA;UACEyE,SAAS,EAAC,uBAAuB;UACjCmB,OAAO,EAAEA,CAAA,KAAMpD,UAAU,CAACL,IAAI,EAAEsB,OAAO,CAACvC,GAAG,CAAE;UAAAwD,QAAA,EAC9C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED9E,OAAA;UACEyE,SAAS,EAAC,0BAA0B;UACpCmB,OAAO,EAAEA,CAAA,KAAM;YACb,MAAMtC,GAAG,GAAG,GAAGb,MAAM,CAACoD,QAAQ,CAACC,MAAM,IAAI3D,IAAI,IAAIsB,OAAO,CAACvC,GAAG,EAAE;YAC9D6E,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC3C,GAAG,CAAC;YAClCX,KAAK,CAAC,kCAAkC,CAAC;UAC3C,CAAE;UAAA+B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9E,OAAA;UACEyE,SAAS,EAAC,uBAAuB;UACjCmB,OAAO,EAAEA,CAAA,KAAM3C,aAAa,CAACd,IAAI,EAAEsB,OAAO,CAACvC,GAAG,CAAE;UAChDgF,KAAK,EAAE,eAAe/D,IAAI,cAAe;UAAAuC,QAAA,EAC1C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,IAAIvE,OAAO,EAAE;IACX,oBAAOP,OAAA;MAAKyE,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC3D;EAEA,oBACE9E,OAAA;IAAKyE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B1E,OAAA;MAAKyE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B1E,OAAA;QAAIyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnD9E,OAAA;QAAGyE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9E,OAAA;QACEyE,SAAS,EAAC,iBAAiB;QAC3BmB,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,QAAQ,CAAE;QAAA+D,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELrE,KAAK,iBAAIT,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEjE;IAAK;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEtD9E,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1E,OAAA;QAAKyE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1E,OAAA;UAAA0E,QAAA,GAAI,2BAAe,EAACvE,KAAK,CAACgF,MAAM,EAAC,GAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACtC3E,KAAK,CAACgF,MAAM,KAAK,CAAC,gBACjBnF,OAAA;UAAKyE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1E,OAAA;YAAA0E,QAAA,EAAG;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3B9E,OAAA;YACEyE,SAAS,EAAC,iBAAiB;YAC3BmB,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,QAAQ,CAAE;YAAA+D,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BvE,KAAK,CAACa,GAAG,CAACC,IAAI,iBACbjB,OAAA,CAACuE,WAAW;YAAgBd,OAAO,EAAExC,IAAK;YAACkB,IAAI,EAAC;UAAM,GAApClB,IAAI,CAACC,GAAG;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA8B,CACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1E,OAAA;UAAA0E,QAAA,GAAI,6BAAiB,EAACrE,OAAO,CAAC8E,MAAM,EAAC,GAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1CzE,OAAO,CAAC8E,MAAM,KAAK,CAAC,gBACnBnF,OAAA;UAAKyE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1E,OAAA;YAAA0E,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7B9E,OAAA;YACEyE,SAAS,EAAC,iBAAiB;YAC3BmB,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,QAAQ,CAAE;YAAA+D,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BrE,OAAO,CAACW,GAAG,CAACG,IAAI,iBACfnB,OAAA,CAACuE,WAAW;YAAgBd,OAAO,EAAEtC,IAAK;YAACgB,IAAI,EAAC;UAAM,GAApChB,IAAI,CAACD,GAAG;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA8B,CACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV;AAAC5E,EAAA,CAxUQD,aAAa;EAAA,QAMHL,WAAW;AAAA;AAAAuG,EAAA,GANrBlG,aAAa;AA0UtB,eAAeA,aAAa;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}