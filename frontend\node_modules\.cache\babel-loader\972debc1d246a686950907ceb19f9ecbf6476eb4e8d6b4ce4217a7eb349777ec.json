{"ast": null, "code": "// Mock API for development when backend is not available\nlet polls = [];\nlet quizzes = [];\nlet pollCounter = 1;\nlet quizCounter = 1;\n\n// Generate session codes\nfunction generateSessionCode() {\n  return Math.random().toString(36).substring(2, 8).toUpperCase();\n}\n\n// Add some initial test data\npolls.push({\n  _id: 'poll_test1',\n  sessionCode: 'TEST01',\n  question: 'What is your favorite programming language?',\n  options: [{\n    text: 'JavaScript',\n    voteCount: 5\n  }, {\n    text: 'Python',\n    voteCount: 3\n  }, {\n    text: 'Java',\n    voteCount: 2\n  }, {\n    text: 'C++',\n    voteCount: 1\n  }],\n  isActive: true,\n  showResults: true,\n  totalVotes: 11,\n  participants: [],\n  createdAt: new Date()\n});\nquizzes.push({\n  _id: 'quiz_test1',\n  sessionCode: 'QUIZ01',\n  question: 'What is the capital of France?',\n  options: [{\n    text: 'London',\n    selectedCount: 1\n  }, {\n    text: 'Berlin',\n    selectedCount: 0\n  }, {\n    text: 'Paris',\n    selectedCount: 8\n  }, {\n    text: 'Madrid',\n    selectedCount: 2\n  }],\n  correctAnswer: 2,\n  isActive: true,\n  showResults: true,\n  totalAnswers: 11,\n  participants: [],\n  createdAt: new Date()\n});\nconst mockApi = {\n  // Polls\n  createPoll: async data => {\n    console.log('Mock API: Creating poll', data);\n    const {\n      question,\n      options\n    } = data;\n    if (!question || !options || options.length < 2 || options.length > 5) {\n      throw new Error('Question is required and must have 2-5 options');\n    }\n    const formattedOptions = options.map(option => ({\n      text: option,\n      voteCount: 0\n    }));\n    const sessionCode = generateSessionCode();\n    const poll = {\n      _id: `poll_${pollCounter++}`,\n      sessionCode: sessionCode,\n      question,\n      options: formattedOptions,\n      isActive: true,\n      showResults: true,\n      totalVotes: 0,\n      participants: [],\n      createdAt: new Date()\n    };\n    polls.push(poll);\n    return {\n      data: {\n        ...poll,\n        joinUrl: `http://localhost:3000/poll/${poll._id}`\n      }\n    };\n  },\n  getPolls: async () => {\n    console.log('Mock API: Getting polls');\n    return {\n      data: polls\n    };\n  },\n  getPoll: async id => {\n    console.log('Mock API: Getting poll', id);\n    const poll = polls.find(p => p._id === id);\n    if (!poll) {\n      throw new Error('Poll not found');\n    }\n    return {\n      data: poll\n    };\n  },\n  // Quizzes\n  createQuiz: async data => {\n    console.log('Mock API: Creating quiz', data);\n    const {\n      question,\n      options,\n      correctAnswer\n    } = data;\n    if (!question || !options || options.length < 2 || options.length > 5) {\n      throw new Error('Question is required and must have 2-5 options');\n    }\n    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {\n      throw new Error('Valid correct answer index is required');\n    }\n    const formattedOptions = options.map(option => ({\n      text: option,\n      selectedCount: 0\n    }));\n    const sessionCode = generateSessionCode();\n    const quiz = {\n      _id: `quiz_${quizCounter++}`,\n      sessionCode: sessionCode,\n      question,\n      options: formattedOptions,\n      correctAnswer,\n      isActive: true,\n      showResults: true,\n      totalAnswers: 0,\n      participants: [],\n      createdAt: new Date()\n    };\n    quizzes.push(quiz);\n    return {\n      data: {\n        ...quiz,\n        joinUrl: `http://localhost:3000/quiz/${quiz._id}`\n      }\n    };\n  },\n  getQuizzes: async () => {\n    console.log('Mock API: Getting quizzes');\n    return {\n      data: quizzes\n    };\n  },\n  getQuiz: async id => {\n    console.log('Mock API: Getting quiz', id);\n    const quiz = quizzes.find(q => q._id === id);\n    if (!quiz) {\n      throw new Error('Quiz not found');\n    }\n    return {\n      data: quiz\n    };\n  },\n  // Session lookup\n  getSession: async code => {\n    console.log('Mock API: Looking up session', code);\n    const upperCode = code.toUpperCase();\n\n    // Check polls first\n    const poll = polls.find(p => p.sessionCode === upperCode);\n    if (poll) {\n      return {\n        data: {\n          ...poll,\n          type: 'poll'\n        }\n      };\n    }\n\n    // Check quizzes\n    const quiz = quizzes.find(q => q.sessionCode === upperCode);\n    if (quiz) {\n      return {\n        data: {\n          ...quiz,\n          type: 'quiz'\n        }\n      };\n    }\n    throw new Error('Session not found');\n  },\n  // Voting/Answering\n  vote: async (pollId, data) => {\n    console.log('Mock API: Voting', pollId, data);\n    const poll = polls.find(p => p._id === pollId);\n    if (!poll) {\n      throw new Error('Poll not found');\n    }\n    const {\n      optionIndex\n    } = data;\n    if (optionIndex >= 0 && optionIndex < poll.options.length) {\n      poll.options[optionIndex].voteCount += 1;\n      poll.totalVotes += 1;\n    }\n    return {\n      data: poll\n    };\n  },\n  answer: async (quizId, data) => {\n    console.log('Mock API: Answering', quizId, data);\n    const quiz = quizzes.find(q => q._id === quizId);\n    if (!quiz) {\n      throw new Error('Quiz not found');\n    }\n    const {\n      optionIndex\n    } = data;\n    const isCorrect = optionIndex === quiz.correctAnswer;\n    if (optionIndex >= 0 && optionIndex < quiz.options.length) {\n      quiz.options[optionIndex].selectedCount += 1;\n      quiz.totalAnswers += 1;\n    }\n    return {\n      data: {\n        quiz,\n        isCorrect,\n        correctAnswer: quiz.correctAnswer\n      }\n    };\n  }\n};\nexport default mockApi;", "map": {"version": 3, "names": ["polls", "quizzes", "pollCounter", "quizCounter", "generateSessionCode", "Math", "random", "toString", "substring", "toUpperCase", "push", "_id", "sessionCode", "question", "options", "text", "voteCount", "isActive", "showResults", "totalVotes", "participants", "createdAt", "Date", "selectedCount", "<PERSON><PERSON><PERSON><PERSON>", "totalAnswers", "mockApi", "createPoll", "data", "console", "log", "length", "Error", "formattedOptions", "map", "option", "poll", "joinUrl", "getPolls", "getPoll", "id", "find", "p", "createQuiz", "undefined", "quiz", "getQuizzes", "getQuiz", "q", "getSession", "code", "upperCode", "type", "vote", "pollId", "optionIndex", "answer", "quizId", "isCorrect"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/mockApi.js"], "sourcesContent": ["// Mock API for development when backend is not available\nlet polls = [];\nlet quizzes = [];\nlet pollCounter = 1;\nlet quizCounter = 1;\n\n// Generate session codes\nfunction generateSessionCode() {\n  return Math.random().toString(36).substring(2, 8).toUpperCase();\n}\n\n// Add some initial test data\npolls.push({\n  _id: 'poll_test1',\n  sessionCode: 'TEST01',\n  question: 'What is your favorite programming language?',\n  options: [\n    { text: 'JavaScript', voteCount: 5 },\n    { text: 'Python', voteCount: 3 },\n    { text: 'Java', voteCount: 2 },\n    { text: 'C++', voteCount: 1 }\n  ],\n  isActive: true,\n  showResults: true,\n  totalVotes: 11,\n  participants: [],\n  createdAt: new Date()\n});\n\nquizzes.push({\n  _id: 'quiz_test1',\n  sessionCode: 'QUIZ01',\n  question: 'What is the capital of France?',\n  options: [\n    { text: 'London', selectedCount: 1 },\n    { text: 'Berlin', selectedCount: 0 },\n    { text: 'Paris', selectedCount: 8 },\n    { text: 'Madrid', selectedCount: 2 }\n  ],\n  correctAnswer: 2,\n  isActive: true,\n  showResults: true,\n  totalAnswers: 11,\n  participants: [],\n  createdAt: new Date()\n});\n\nconst mockApi = {\n  // Polls\n  createPoll: async (data) => {\n    console.log('Mock API: Creating poll', data);\n    const { question, options } = data;\n    \n    if (!question || !options || options.length < 2 || options.length > 5) {\n      throw new Error('Question is required and must have 2-5 options');\n    }\n\n    const formattedOptions = options.map(option => ({\n      text: option,\n      voteCount: 0\n    }));\n\n    const sessionCode = generateSessionCode();\n    const poll = {\n      _id: `poll_${pollCounter++}`,\n      sessionCode: sessionCode,\n      question,\n      options: formattedOptions,\n      isActive: true,\n      showResults: true,\n      totalVotes: 0,\n      participants: [],\n      createdAt: new Date()\n    };\n\n    polls.push(poll);\n    \n    return {\n      data: {\n        ...poll,\n        joinUrl: `http://localhost:3000/poll/${poll._id}`\n      }\n    };\n  },\n\n  getPolls: async () => {\n    console.log('Mock API: Getting polls');\n    return { data: polls };\n  },\n\n  getPoll: async (id) => {\n    console.log('Mock API: Getting poll', id);\n    const poll = polls.find(p => p._id === id);\n    if (!poll) {\n      throw new Error('Poll not found');\n    }\n    return { data: poll };\n  },\n\n  // Quizzes\n  createQuiz: async (data) => {\n    console.log('Mock API: Creating quiz', data);\n    const { question, options, correctAnswer } = data;\n    \n    if (!question || !options || options.length < 2 || options.length > 5) {\n      throw new Error('Question is required and must have 2-5 options');\n    }\n\n    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {\n      throw new Error('Valid correct answer index is required');\n    }\n\n    const formattedOptions = options.map(option => ({\n      text: option,\n      selectedCount: 0\n    }));\n\n    const sessionCode = generateSessionCode();\n    const quiz = {\n      _id: `quiz_${quizCounter++}`,\n      sessionCode: sessionCode,\n      question,\n      options: formattedOptions,\n      correctAnswer,\n      isActive: true,\n      showResults: true,\n      totalAnswers: 0,\n      participants: [],\n      createdAt: new Date()\n    };\n\n    quizzes.push(quiz);\n    \n    return {\n      data: {\n        ...quiz,\n        joinUrl: `http://localhost:3000/quiz/${quiz._id}`\n      }\n    };\n  },\n\n  getQuizzes: async () => {\n    console.log('Mock API: Getting quizzes');\n    return { data: quizzes };\n  },\n\n  getQuiz: async (id) => {\n    console.log('Mock API: Getting quiz', id);\n    const quiz = quizzes.find(q => q._id === id);\n    if (!quiz) {\n      throw new Error('Quiz not found');\n    }\n    return { data: quiz };\n  },\n\n  // Session lookup\n  getSession: async (code) => {\n    console.log('Mock API: Looking up session', code);\n    const upperCode = code.toUpperCase();\n\n    // Check polls first\n    const poll = polls.find(p => p.sessionCode === upperCode);\n    if (poll) {\n      return { data: { ...poll, type: 'poll' } };\n    }\n\n    // Check quizzes\n    const quiz = quizzes.find(q => q.sessionCode === upperCode);\n    if (quiz) {\n      return { data: { ...quiz, type: 'quiz' } };\n    }\n\n    throw new Error('Session not found');\n  },\n\n  // Voting/Answering\n  vote: async (pollId, data) => {\n    console.log('Mock API: Voting', pollId, data);\n    const poll = polls.find(p => p._id === pollId);\n    if (!poll) {\n      throw new Error('Poll not found');\n    }\n\n    const { optionIndex } = data;\n    if (optionIndex >= 0 && optionIndex < poll.options.length) {\n      poll.options[optionIndex].voteCount += 1;\n      poll.totalVotes += 1;\n    }\n\n    return { data: poll };\n  },\n\n  answer: async (quizId, data) => {\n    console.log('Mock API: Answering', quizId, data);\n    const quiz = quizzes.find(q => q._id === quizId);\n    if (!quiz) {\n      throw new Error('Quiz not found');\n    }\n\n    const { optionIndex } = data;\n    const isCorrect = optionIndex === quiz.correctAnswer;\n    \n    if (optionIndex >= 0 && optionIndex < quiz.options.length) {\n      quiz.options[optionIndex].selectedCount += 1;\n      quiz.totalAnswers += 1;\n    }\n\n    return { \n      data: {\n        quiz,\n        isCorrect,\n        correctAnswer: quiz.correctAnswer\n      }\n    };\n  }\n};\n\nexport default mockApi;\n"], "mappings": "AAAA;AACA,IAAIA,KAAK,GAAG,EAAE;AACd,IAAIC,OAAO,GAAG,EAAE;AAChB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,WAAW,GAAG,CAAC;;AAEnB;AACA,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACjE;;AAEA;AACAT,KAAK,CAACU,IAAI,CAAC;EACTC,GAAG,EAAE,YAAY;EACjBC,WAAW,EAAE,QAAQ;EACrBC,QAAQ,EAAE,6CAA6C;EACvDC,OAAO,EAAE,CACP;IAAEC,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAE,CAAC,EACpC;IAAED,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAE;EAAE,CAAC,EAChC;IAAED,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAE;EAAE,CAAC,EAC9B;IAAED,IAAI,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAE,CAAC,CAC9B;EACDC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC;AACtB,CAAC,CAAC;AAEFrB,OAAO,CAACS,IAAI,CAAC;EACXC,GAAG,EAAE,YAAY;EACjBC,WAAW,EAAE,QAAQ;EACrBC,QAAQ,EAAE,gCAAgC;EAC1CC,OAAO,EAAE,CACP;IAAEC,IAAI,EAAE,QAAQ;IAAEQ,aAAa,EAAE;EAAE,CAAC,EACpC;IAAER,IAAI,EAAE,QAAQ;IAAEQ,aAAa,EAAE;EAAE,CAAC,EACpC;IAAER,IAAI,EAAE,OAAO;IAAEQ,aAAa,EAAE;EAAE,CAAC,EACnC;IAAER,IAAI,EAAE,QAAQ;IAAEQ,aAAa,EAAE;EAAE,CAAC,CACrC;EACDC,aAAa,EAAE,CAAC;EAChBP,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBO,YAAY,EAAE,EAAE;EAChBL,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC;AACtB,CAAC,CAAC;AAEF,MAAMI,OAAO,GAAG;EACd;EACAC,UAAU,EAAE,MAAOC,IAAI,IAAK;IAC1BC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,IAAI,CAAC;IAC5C,MAAM;MAAEf,QAAQ;MAAEC;IAAQ,CAAC,GAAGc,IAAI;IAElC,IAAI,CAACf,QAAQ,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACiB,MAAM,GAAG,CAAC,IAAIjB,OAAO,CAACiB,MAAM,GAAG,CAAC,EAAE;MACrE,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA,MAAMC,gBAAgB,GAAGnB,OAAO,CAACoB,GAAG,CAACC,MAAM,KAAK;MAC9CpB,IAAI,EAAEoB,MAAM;MACZnB,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;IAEH,MAAMJ,WAAW,GAAGR,mBAAmB,CAAC,CAAC;IACzC,MAAMgC,IAAI,GAAG;MACXzB,GAAG,EAAE,QAAQT,WAAW,EAAE,EAAE;MAC5BU,WAAW,EAAEA,WAAW;MACxBC,QAAQ;MACRC,OAAO,EAAEmB,gBAAgB;MACzBhB,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDtB,KAAK,CAACU,IAAI,CAAC0B,IAAI,CAAC;IAEhB,OAAO;MACLR,IAAI,EAAE;QACJ,GAAGQ,IAAI;QACPC,OAAO,EAAE,8BAA8BD,IAAI,CAACzB,GAAG;MACjD;IACF,CAAC;EACH,CAAC;EAED2B,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpBT,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,OAAO;MAAEF,IAAI,EAAE5B;IAAM,CAAC;EACxB,CAAC;EAEDuC,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrBX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEU,EAAE,CAAC;IACzC,MAAMJ,IAAI,GAAGpC,KAAK,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,GAAG,KAAK6B,EAAE,CAAC;IAC1C,IAAI,CAACJ,IAAI,EAAE;MACT,MAAM,IAAIJ,KAAK,CAAC,gBAAgB,CAAC;IACnC;IACA,OAAO;MAAEJ,IAAI,EAAEQ;IAAK,CAAC;EACvB,CAAC;EAED;EACAO,UAAU,EAAE,MAAOf,IAAI,IAAK;IAC1BC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,IAAI,CAAC;IAC5C,MAAM;MAAEf,QAAQ;MAAEC,OAAO;MAAEU;IAAc,CAAC,GAAGI,IAAI;IAEjD,IAAI,CAACf,QAAQ,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACiB,MAAM,GAAG,CAAC,IAAIjB,OAAO,CAACiB,MAAM,GAAG,CAAC,EAAE;MACrE,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA,IAAIR,aAAa,KAAKoB,SAAS,IAAIpB,aAAa,GAAG,CAAC,IAAIA,aAAa,IAAIV,OAAO,CAACiB,MAAM,EAAE;MACvF,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IAEA,MAAMC,gBAAgB,GAAGnB,OAAO,CAACoB,GAAG,CAACC,MAAM,KAAK;MAC9CpB,IAAI,EAAEoB,MAAM;MACZZ,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;IAEH,MAAMX,WAAW,GAAGR,mBAAmB,CAAC,CAAC;IACzC,MAAMyC,IAAI,GAAG;MACXlC,GAAG,EAAE,QAAQR,WAAW,EAAE,EAAE;MAC5BS,WAAW,EAAEA,WAAW;MACxBC,QAAQ;MACRC,OAAO,EAAEmB,gBAAgB;MACzBT,aAAa;MACbP,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,IAAI;MACjBO,YAAY,EAAE,CAAC;MACfL,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDrB,OAAO,CAACS,IAAI,CAACmC,IAAI,CAAC;IAElB,OAAO;MACLjB,IAAI,EAAE;QACJ,GAAGiB,IAAI;QACPR,OAAO,EAAE,8BAA8BQ,IAAI,CAAClC,GAAG;MACjD;IACF,CAAC;EACH,CAAC;EAEDmC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtBjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,OAAO;MAAEF,IAAI,EAAE3B;IAAQ,CAAC;EAC1B,CAAC;EAED8C,OAAO,EAAE,MAAOP,EAAE,IAAK;IACrBX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEU,EAAE,CAAC;IACzC,MAAMK,IAAI,GAAG5C,OAAO,CAACwC,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACrC,GAAG,KAAK6B,EAAE,CAAC;IAC5C,IAAI,CAACK,IAAI,EAAE;MACT,MAAM,IAAIb,KAAK,CAAC,gBAAgB,CAAC;IACnC;IACA,OAAO;MAAEJ,IAAI,EAAEiB;IAAK,CAAC;EACvB,CAAC;EAED;EACAI,UAAU,EAAE,MAAOC,IAAI,IAAK;IAC1BrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoB,IAAI,CAAC;IACjD,MAAMC,SAAS,GAAGD,IAAI,CAACzC,WAAW,CAAC,CAAC;;IAEpC;IACA,MAAM2B,IAAI,GAAGpC,KAAK,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,WAAW,KAAKuC,SAAS,CAAC;IACzD,IAAIf,IAAI,EAAE;MACR,OAAO;QAAER,IAAI,EAAE;UAAE,GAAGQ,IAAI;UAAEgB,IAAI,EAAE;QAAO;MAAE,CAAC;IAC5C;;IAEA;IACA,MAAMP,IAAI,GAAG5C,OAAO,CAACwC,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACpC,WAAW,KAAKuC,SAAS,CAAC;IAC3D,IAAIN,IAAI,EAAE;MACR,OAAO;QAAEjB,IAAI,EAAE;UAAE,GAAGiB,IAAI;UAAEO,IAAI,EAAE;QAAO;MAAE,CAAC;IAC5C;IAEA,MAAM,IAAIpB,KAAK,CAAC,mBAAmB,CAAC;EACtC,CAAC;EAED;EACAqB,IAAI,EAAE,MAAAA,CAAOC,MAAM,EAAE1B,IAAI,KAAK;IAC5BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwB,MAAM,EAAE1B,IAAI,CAAC;IAC7C,MAAMQ,IAAI,GAAGpC,KAAK,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,GAAG,KAAK2C,MAAM,CAAC;IAC9C,IAAI,CAAClB,IAAI,EAAE;MACT,MAAM,IAAIJ,KAAK,CAAC,gBAAgB,CAAC;IACnC;IAEA,MAAM;MAAEuB;IAAY,CAAC,GAAG3B,IAAI;IAC5B,IAAI2B,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAGnB,IAAI,CAACtB,OAAO,CAACiB,MAAM,EAAE;MACzDK,IAAI,CAACtB,OAAO,CAACyC,WAAW,CAAC,CAACvC,SAAS,IAAI,CAAC;MACxCoB,IAAI,CAACjB,UAAU,IAAI,CAAC;IACtB;IAEA,OAAO;MAAES,IAAI,EAAEQ;IAAK,CAAC;EACvB,CAAC;EAEDoB,MAAM,EAAE,MAAAA,CAAOC,MAAM,EAAE7B,IAAI,KAAK;IAC9BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,MAAM,EAAE7B,IAAI,CAAC;IAChD,MAAMiB,IAAI,GAAG5C,OAAO,CAACwC,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACrC,GAAG,KAAK8C,MAAM,CAAC;IAChD,IAAI,CAACZ,IAAI,EAAE;MACT,MAAM,IAAIb,KAAK,CAAC,gBAAgB,CAAC;IACnC;IAEA,MAAM;MAAEuB;IAAY,CAAC,GAAG3B,IAAI;IAC5B,MAAM8B,SAAS,GAAGH,WAAW,KAAKV,IAAI,CAACrB,aAAa;IAEpD,IAAI+B,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAGV,IAAI,CAAC/B,OAAO,CAACiB,MAAM,EAAE;MACzDc,IAAI,CAAC/B,OAAO,CAACyC,WAAW,CAAC,CAAChC,aAAa,IAAI,CAAC;MAC5CsB,IAAI,CAACpB,YAAY,IAAI,CAAC;IACxB;IAEA,OAAO;MACLG,IAAI,EAAE;QACJiB,IAAI;QACJa,SAAS;QACTlC,aAAa,EAAEqB,IAAI,CAACrB;MACtB;IACF,CAAC;EACH;AACF,CAAC;AAED,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}