{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lpqa cpy\\\\frontend\\\\src\\\\pages\\\\QuizSession.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams } from 'react-router-dom';\nimport api from '../api';\nimport socket from '../socket';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction QuizSession() {\n  _s();\n  var _quiz$options$feedbac, _quiz$participants;\n  const {\n    id\n  } = useParams();\n  const [quiz, setQuiz] = useState(null);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [hasAnswered, setHasAnswered] = useState(false);\n  const [feedback, setFeedback] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const fetchQuiz = useCallback(async () => {\n    try {\n      var _response$data$partic;\n      console.log('🔍 Fetching quiz with ID:', id);\n      const response = await api.get(`/quizzes/${id}`);\n      console.log('✅ Quiz fetched successfully:', response.data);\n      setQuiz(response.data);\n\n      // Check if user already answered\n      const participant = (_response$data$partic = response.data.participants) === null || _response$data$partic === void 0 ? void 0 : _response$data$partic.find(p => p.socketId === socket.id);\n      if (participant && participant.hasAnswered) {\n        setHasAnswered(true);\n        setSelectedOption(participant.selectedOption);\n        setFeedback({\n          isCorrect: participant.isCorrect,\n          correctAnswer: response.data.correctAnswer,\n          selectedAnswer: participant.selectedOption\n        });\n      }\n    } catch (err) {\n      var _err$response, _err$response2, _err$response2$data;\n      console.error('❌ Quiz fetch error:', err);\n      if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 404) {\n        setError('Quiz not found. Please check the URL or contact the quiz creator.');\n      } else if ((_err$response2 = err.response) !== null && _err$response2 !== void 0 && (_err$response2$data = _err$response2.data) !== null && _err$response2$data !== void 0 && _err$response2$data.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Failed to load quiz. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n  useEffect(() => {\n    fetchQuiz();\n\n    // Join quiz room\n    socket.emit('join-quiz', id);\n\n    // Listen for real-time updates\n    socket.on('quiz-update', data => {\n      setQuiz(data.quiz);\n    });\n    socket.on('results-visibility-changed', data => {\n      setQuiz(data.quiz);\n    });\n    socket.on('answer-feedback', data => {\n      setFeedback(data);\n      setHasAnswered(true);\n    });\n    socket.on('error', data => {\n      setError(data.message);\n    });\n    return () => {\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n      socket.off('answer-feedback');\n      socket.off('error');\n    };\n  }, [id, fetchQuiz]);\n  const handleAnswer = optionIndex => {\n    if (hasAnswered || !quiz.isActive) return;\n    setSelectedOption(optionIndex);\n\n    // Emit answer via socket\n    socket.emit('answer', {\n      quizId: id,\n      optionIndex,\n      socketId: socket.id\n    });\n  };\n  const getTotalAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options.reduce((total, option) => total + option.selectedCount, 0);\n  };\n  const getPercentage = selectedCount => {\n    const total = getTotalAnswers();\n    return total === 0 ? 0 : Math.round(selectedCount / total * 100);\n  };\n  const getCorrectAnswers = () => {\n    var _quiz$options$quiz$co;\n    if (!quiz) return 0;\n    return ((_quiz$options$quiz$co = quiz.options[quiz.correctAnswer]) === null || _quiz$options$quiz$co === void 0 ? void 0 : _quiz$options$quiz$co.selectedCount) || 0;\n  };\n  const getSuccessRate = () => {\n    const total = getTotalAnswers();\n    const correct = getCorrectAnswers();\n    return total > 0 ? Math.round(correct / total * 100) : 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading quiz...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 12\n    }, this);\n  }\n  if (!quiz) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: \"Quiz not found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"session-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-type\",\n        children: \"\\uD83E\\uDDE0 Live Quiz\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `session-status ${quiz.isActive ? 'active' : 'ended'}`,\n        children: quiz.isActive ? 'Active' : 'Ended'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"question-text\",\n        children: quiz.question\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), !hasAnswered && quiz.isActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"instruction\",\n          children: \"Choose your answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"options-list\",\n          children: quiz.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"option-item\",\n            onClick: () => handleAnswer(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-letter\",\n              children: String.fromCharCode(65 + index)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-text\",\n              children: option.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feedback-container\",\n        children: hasAnswered && feedback ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-icon\",\n            children: feedback.isCorrect ? '🎉' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-text\",\n            children: feedback.isCorrect ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Correct!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Great job! You got it right.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Incorrect.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"The correct answer was: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: (_quiz$options$feedbac = quiz.options[feedback.correctAnswer]) === null || _quiz$options$feedbac === void 0 ? void 0 : _quiz$options$feedbac.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 50\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this) : !quiz.isActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feedback info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-icon\",\n            children: \"\\u23F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feedback-text\",\n            children: \"This quiz has ended. No more answers are being accepted.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 15\n        }, this) : null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), quiz.showResults && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"results-title\",\n        children: [\"\\uD83D\\uDCCA Live Results (\", getTotalAnswers(), \" total answers)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: getCorrectAnswers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Correct Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: getTotalAnswers() - getCorrectAnswers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Incorrect Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: [getSuccessRate(), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Success Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-list\",\n        children: quiz.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-option\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-letter\",\n                children: String.fromCharCode(65 + index)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-text\",\n                children: [option.text, index === quiz.correctAnswer && ' ✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"result-count\",\n              children: [option.selectedCount, \" answers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `result-fill ${index === quiz.correctAnswer ? 'correct' : ''} ${selectedOption === index ? 'user-selected' : ''}`,\n              style: {\n                width: `${getPercentage(option.selectedCount)}%`\n              },\n              children: [getPercentage(option.selectedCount), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this), !quiz.showResults && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCCA Results Hidden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The quiz creator has hidden the results. They will be visible when the creator chooses to show them.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Session ID: \", quiz._id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Participants: \", ((_quiz$participants = quiz.participants) === null || _quiz$participants === void 0 ? void 0 : _quiz$participants.length) || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(QuizSession, \"L86uHG89nVL7wgoYkYagbPUe8wM=\", false, function () {\n  return [useParams];\n});\n_c = QuizSession;\nexport default QuizSession;\nvar _c;\n$RefreshReg$(_c, \"QuizSession\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "api", "socket", "jsxDEV", "_jsxDEV", "QuizSession", "_s", "_quiz$options$feedbac", "_quiz$participants", "id", "quiz", "setQuiz", "selectedOption", "setSelectedOption", "hasAnswered", "setHasAnswered", "feedback", "setFeedback", "loading", "setLoading", "error", "setError", "fetchQuiz", "_response$data$partic", "console", "log", "response", "get", "data", "participant", "participants", "find", "p", "socketId", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "err", "_err$response", "_err$response2", "_err$response2$data", "status", "emit", "on", "message", "off", "handleAnswer", "optionIndex", "isActive", "quizId", "getTotalAnswers", "options", "reduce", "total", "option", "selectedCount", "getPercentage", "Math", "round", "getCorrectAnswers", "_quiz$options$quiz$co", "getSuccessRate", "correct", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "question", "map", "index", "onClick", "String", "fromCharCode", "text", "showResults", "style", "width", "_id", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/pages/QuizSession.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams } from 'react-router-dom';\nimport api from '../api';\nimport socket from '../socket';\n\nfunction QuizSession() {\n  const { id } = useParams();\n  const [quiz, setQuiz] = useState(null);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [hasAnswered, setHasAnswered] = useState(false);\n  const [feedback, setFeedback] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  const fetchQuiz = useCallback(async () => {\n    try {\n      console.log('🔍 Fetching quiz with ID:', id);\n      const response = await api.get(`/quizzes/${id}`);\n      console.log('✅ Quiz fetched successfully:', response.data);\n      setQuiz(response.data);\n\n      // Check if user already answered\n      const participant = response.data.participants?.find(p => p.socketId === socket.id);\n      if (participant && participant.hasAnswered) {\n        setHasAnswered(true);\n        setSelectedOption(participant.selectedOption);\n        setFeedback({\n          isCorrect: participant.isCorrect,\n          correctAnswer: response.data.correctAnswer,\n          selectedAnswer: participant.selectedOption\n        });\n      }\n    } catch (err) {\n      console.error('❌ Quiz fetch error:', err);\n      if (err.response?.status === 404) {\n        setError('Quiz not found. Please check the URL or contact the quiz creator.');\n      } else if (err.response?.data?.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Failed to load quiz. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchQuiz();\n\n    // Join quiz room\n    socket.emit('join-quiz', id);\n\n    // Listen for real-time updates\n    socket.on('quiz-update', (data) => {\n      setQuiz(data.quiz);\n    });\n\n    socket.on('results-visibility-changed', (data) => {\n      setQuiz(data.quiz);\n    });\n\n    socket.on('answer-feedback', (data) => {\n      setFeedback(data);\n      setHasAnswered(true);\n    });\n\n    socket.on('error', (data) => {\n      setError(data.message);\n    });\n\n    return () => {\n      socket.off('quiz-update');\n      socket.off('results-visibility-changed');\n      socket.off('answer-feedback');\n      socket.off('error');\n    };\n  }, [id, fetchQuiz]);\n\n  const handleAnswer = (optionIndex) => {\n    if (hasAnswered || !quiz.isActive) return;\n    \n    setSelectedOption(optionIndex);\n    \n    // Emit answer via socket\n    socket.emit('answer', {\n      quizId: id,\n      optionIndex,\n      socketId: socket.id\n    });\n  };\n\n  const getTotalAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options.reduce((total, option) => total + option.selectedCount, 0);\n  };\n\n  const getPercentage = (selectedCount) => {\n    const total = getTotalAnswers();\n    return total === 0 ? 0 : Math.round((selectedCount / total) * 100);\n  };\n\n  const getCorrectAnswers = () => {\n    if (!quiz) return 0;\n    return quiz.options[quiz.correctAnswer]?.selectedCount || 0;\n  };\n\n  const getSuccessRate = () => {\n    const total = getTotalAnswers();\n    const correct = getCorrectAnswers();\n    return total > 0 ? Math.round((correct / total) * 100) : 0;\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading quiz...</div>;\n  }\n\n  if (error) {\n    return <div className=\"error\">{error}</div>;\n  }\n\n  if (!quiz) {\n    return <div className=\"error\">Quiz not found</div>;\n  }\n\n  return (\n    <div className=\"session-container\">\n      <div className=\"session-header\">\n        <div className=\"session-type\">🧠 Live Quiz</div>\n        <div className={`session-status ${quiz.isActive ? 'active' : 'ended'}`}>\n          {quiz.isActive ? 'Active' : 'Ended'}\n        </div>\n      </div>\n\n      <div className=\"question-card\">\n        <h2 className=\"question-text\">{quiz.question}</h2>\n        \n        {!hasAnswered && quiz.isActive ? (\n          <div className=\"options-container\">\n            <p className=\"instruction\">Choose your answer:</p>\n            <ul className=\"options-list\">\n              {quiz.options.map((option, index) => (\n                <li \n                  key={index} \n                  className=\"option-item\"\n                  onClick={() => handleAnswer(index)}\n                >\n                  <div className=\"option-letter\">\n                    {String.fromCharCode(65 + index)}\n                  </div>\n                  <div className=\"option-text\">{option.text}</div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        ) : (\n          <div className=\"feedback-container\">\n            {hasAnswered && feedback ? (\n              <div className={`feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`}>\n                <div className=\"feedback-icon\">\n                  {feedback.isCorrect ? '🎉' : '❌'}\n                </div>\n                <div className=\"feedback-text\">\n                  {feedback.isCorrect ? (\n                    <div>\n                      <strong>Correct!</strong>\n                      <p>Great job! You got it right.</p>\n                    </div>\n                  ) : (\n                    <div>\n                      <strong>Incorrect.</strong>\n                      <p>The correct answer was: <strong>{quiz.options[feedback.correctAnswer]?.text}</strong></p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ) : !quiz.isActive ? (\n              <div className=\"feedback info\">\n                <div className=\"feedback-icon\">⏰</div>\n                <div className=\"feedback-text\">\n                  This quiz has ended. No more answers are being accepted.\n                </div>\n              </div>\n            ) : null}\n          </div>\n        )}\n      </div>\n\n      {/* Show results based on quiz settings */}\n      {quiz.showResults && (\n        <div className=\"results-card\">\n          <h3 className=\"results-title\">\n            📊 Live Results ({getTotalAnswers()} total answers)\n          </h3>\n\n          <div className=\"quiz-stats\">\n            <div className=\"stat-item\">\n              <span className=\"stat-value\">{getCorrectAnswers()}</span>\n              <span className=\"stat-label\">Correct Answers</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-value\">{getTotalAnswers() - getCorrectAnswers()}</span>\n              <span className=\"stat-label\">Incorrect Answers</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-value\">{getSuccessRate()}%</span>\n              <span className=\"stat-label\">Success Rate</span>\n            </div>\n          </div>\n\n          <div className=\"results-list\">\n            {quiz.options.map((option, index) => (\n              <div key={index} className=\"result-item\">\n                <div className=\"result-header\">\n                  <div className=\"result-option\">\n                    <div className=\"option-letter\">\n                      {String.fromCharCode(65 + index)}\n                    </div>\n                    <span className=\"option-text\">\n                      {option.text}\n                      {index === quiz.correctAnswer && ' ✅'}\n                    </span>\n                  </div>\n                  <span className=\"result-count\">{option.selectedCount} answers</span>\n                </div>\n                <div className=\"result-bar\">\n                  <div\n                    className={`result-fill ${\n                      index === quiz.correctAnswer ? 'correct' : ''\n                    } ${selectedOption === index ? 'user-selected' : ''}`}\n                    style={{ width: `${getPercentage(option.selectedCount)}%` }}\n                  >\n                    {getPercentage(option.selectedCount)}%\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {!quiz.showResults && (\n        <div className=\"results-hidden\">\n          <h3>📊 Results Hidden</h3>\n          <p>The quiz creator has hidden the results. They will be visible when the creator chooses to show them.</p>\n        </div>\n      )}\n\n      <div className=\"session-info\">\n        <p>Session ID: {quiz._id}</p>\n        <p>Participants: {quiz.participants?.length || 0}</p>\n      </div>\n    </div>\n  );\n}\n\nexport default QuizSession;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,GAAG,MAAM,QAAQ;AACxB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMyB,SAAS,GAAGvB,WAAW,CAAC,YAAY;IACxC,IAAI;MAAA,IAAAwB,qBAAA;MACFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhB,EAAE,CAAC;MAC5C,MAAMiB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,YAAYlB,EAAE,EAAE,CAAC;MAChDe,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC1DjB,OAAO,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAEtB;MACA,MAAMC,WAAW,IAAAN,qBAAA,GAAGG,QAAQ,CAACE,IAAI,CAACE,YAAY,cAAAP,qBAAA,uBAA1BA,qBAAA,CAA4BQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK/B,MAAM,CAACO,EAAE,CAAC;MACnF,IAAIoB,WAAW,IAAIA,WAAW,CAACf,WAAW,EAAE;QAC1CC,cAAc,CAAC,IAAI,CAAC;QACpBF,iBAAiB,CAACgB,WAAW,CAACjB,cAAc,CAAC;QAC7CK,WAAW,CAAC;UACViB,SAAS,EAAEL,WAAW,CAACK,SAAS;UAChCC,aAAa,EAAET,QAAQ,CAACE,IAAI,CAACO,aAAa;UAC1CC,cAAc,EAAEP,WAAW,CAACjB;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOyB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZhB,OAAO,CAACJ,KAAK,CAAC,qBAAqB,EAAEiB,GAAG,CAAC;MACzC,IAAI,EAAAC,aAAA,GAAAD,GAAG,CAACX,QAAQ,cAAAY,aAAA,uBAAZA,aAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;QAChCpB,QAAQ,CAAC,mEAAmE,CAAC;MAC/E,CAAC,MAAM,KAAAkB,cAAA,GAAIF,GAAG,CAACX,QAAQ,cAAAa,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,eAAlBA,mBAAA,CAAoBpB,KAAK,EAAE;QACpCC,QAAQ,CAACgB,GAAG,CAACX,QAAQ,CAACE,IAAI,CAACR,KAAK,CAAC;MACnC,CAAC,MAAM;QACLC,QAAQ,CAAC,wCAAwC,CAAC;MACpD;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;EAERX,SAAS,CAAC,MAAM;IACdwB,SAAS,CAAC,CAAC;;IAEX;IACApB,MAAM,CAACwC,IAAI,CAAC,WAAW,EAAEjC,EAAE,CAAC;;IAE5B;IACAP,MAAM,CAACyC,EAAE,CAAC,aAAa,EAAGf,IAAI,IAAK;MACjCjB,OAAO,CAACiB,IAAI,CAAClB,IAAI,CAAC;IACpB,CAAC,CAAC;IAEFR,MAAM,CAACyC,EAAE,CAAC,4BAA4B,EAAGf,IAAI,IAAK;MAChDjB,OAAO,CAACiB,IAAI,CAAClB,IAAI,CAAC;IACpB,CAAC,CAAC;IAEFR,MAAM,CAACyC,EAAE,CAAC,iBAAiB,EAAGf,IAAI,IAAK;MACrCX,WAAW,CAACW,IAAI,CAAC;MACjBb,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC;IAEFb,MAAM,CAACyC,EAAE,CAAC,OAAO,EAAGf,IAAI,IAAK;MAC3BP,QAAQ,CAACO,IAAI,CAACgB,OAAO,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,MAAM;MACX1C,MAAM,CAAC2C,GAAG,CAAC,aAAa,CAAC;MACzB3C,MAAM,CAAC2C,GAAG,CAAC,4BAA4B,CAAC;MACxC3C,MAAM,CAAC2C,GAAG,CAAC,iBAAiB,CAAC;MAC7B3C,MAAM,CAAC2C,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACpC,EAAE,EAAEa,SAAS,CAAC,CAAC;EAEnB,MAAMwB,YAAY,GAAIC,WAAW,IAAK;IACpC,IAAIjC,WAAW,IAAI,CAACJ,IAAI,CAACsC,QAAQ,EAAE;IAEnCnC,iBAAiB,CAACkC,WAAW,CAAC;;IAE9B;IACA7C,MAAM,CAACwC,IAAI,CAAC,QAAQ,EAAE;MACpBO,MAAM,EAAExC,EAAE;MACVsC,WAAW;MACXd,QAAQ,EAAE/B,MAAM,CAACO;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACxC,IAAI,EAAE,OAAO,CAAC;IACnB,OAAOA,IAAI,CAACyC,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACC,aAAa,EAAE,CAAC,CAAC;EAChF,CAAC;EAED,MAAMC,aAAa,GAAID,aAAa,IAAK;IACvC,MAAMF,KAAK,GAAGH,eAAe,CAAC,CAAC;IAC/B,OAAOG,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAEH,aAAa,GAAGF,KAAK,GAAI,GAAG,CAAC;EACpE,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC9B,IAAI,CAAClD,IAAI,EAAE,OAAO,CAAC;IACnB,OAAO,EAAAkD,qBAAA,GAAAlD,IAAI,CAACyC,OAAO,CAACzC,IAAI,CAACyB,aAAa,CAAC,cAAAyB,qBAAA,uBAAhCA,qBAAA,CAAkCL,aAAa,KAAI,CAAC;EAC7D,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMR,KAAK,GAAGH,eAAe,CAAC,CAAC;IAC/B,MAAMY,OAAO,GAAGH,iBAAiB,CAAC,CAAC;IACnC,OAAON,KAAK,GAAG,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAEI,OAAO,GAAGT,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;EAC5D,CAAC;EAED,IAAInC,OAAO,EAAE;IACX,oBAAOd,OAAA;MAAK2D,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvD;EAEA,IAAIhD,KAAK,EAAE;IACT,oBAAOhB,OAAA;MAAK2D,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE5C;IAAK;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7C;EAEA,IAAI,CAAC1D,IAAI,EAAE;IACT,oBAAON,OAAA;MAAK2D,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpD;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC5D,OAAA;MAAK2D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5D,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChDhE,OAAA;QAAK2D,SAAS,EAAE,kBAAkBrD,IAAI,CAACsC,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAG;QAAAgB,QAAA,EACpEtD,IAAI,CAACsC,QAAQ,GAAG,QAAQ,GAAG;MAAO;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhE,OAAA;MAAK2D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B5D,OAAA;QAAI2D,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEtD,IAAI,CAAC2D;MAAQ;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEjD,CAACtD,WAAW,IAAIJ,IAAI,CAACsC,QAAQ,gBAC5B5C,OAAA;QAAK2D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5D,OAAA;UAAG2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClDhE,OAAA;UAAI2D,SAAS,EAAC,cAAc;UAAAC,QAAA,EACzBtD,IAAI,CAACyC,OAAO,CAACmB,GAAG,CAAC,CAAChB,MAAM,EAAEiB,KAAK,kBAC9BnE,OAAA;YAEE2D,SAAS,EAAC,aAAa;YACvBS,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACyB,KAAK,CAAE;YAAAP,QAAA,gBAEnC5D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BS,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEV,MAAM,CAACqB;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP3CG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQR,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAENhE,OAAA;QAAK2D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChClD,WAAW,IAAIE,QAAQ,gBACtBZ,OAAA;UAAK2D,SAAS,EAAE,YAAY/C,QAAQ,CAACkB,SAAS,GAAG,SAAS,GAAG,WAAW,EAAG;UAAA8B,QAAA,gBACzE5D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BhD,QAAQ,CAACkB,SAAS,GAAG,IAAI,GAAG;UAAG;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BhD,QAAQ,CAACkB,SAAS,gBACjB9B,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAA4D,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzBhE,OAAA;gBAAA4D,QAAA,EAAG;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,gBAENhE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAA4D,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3BhE,OAAA;gBAAA4D,QAAA,GAAG,0BAAwB,eAAA5D,OAAA;kBAAA4D,QAAA,GAAAzD,qBAAA,GAASG,IAAI,CAACyC,OAAO,CAACnC,QAAQ,CAACmB,aAAa,CAAC,cAAA5B,qBAAA,uBAApCA,qBAAA,CAAsCoE;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ,CAAC1D,IAAI,CAACsC,QAAQ,gBAChB5C,OAAA;UAAK2D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtChE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL1D,IAAI,CAACkE,WAAW,iBACfxE,OAAA;MAAK2D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B5D,OAAA;QAAI2D,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,6BACX,EAACd,eAAe,CAAC,CAAC,EAAC,iBACtC;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELhE,OAAA;QAAK2D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5D,OAAA;UAAK2D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5D,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEL,iBAAiB,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzDhE,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNhE,OAAA;UAAK2D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5D,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,eAAe,CAAC,CAAC,GAAGS,iBAAiB,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7EhE,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNhE,OAAA;UAAK2D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5D,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEH,cAAc,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDhE,OAAA;YAAM2D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BtD,IAAI,CAACyC,OAAO,CAACmB,GAAG,CAAC,CAAChB,MAAM,EAAEiB,KAAK,kBAC9BnE,OAAA;UAAiB2D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtC5D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5D,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BS,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNhE,OAAA;gBAAM2D,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAC1BV,MAAM,CAACqB,IAAI,EACXJ,KAAK,KAAK7D,IAAI,CAACyB,aAAa,IAAI,IAAI;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhE,OAAA;cAAM2D,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAEV,MAAM,CAACC,aAAa,EAAC,UAAQ;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzB5D,OAAA;cACE2D,SAAS,EAAE,eACTQ,KAAK,KAAK7D,IAAI,CAACyB,aAAa,GAAG,SAAS,GAAG,EAAE,IAC3CvB,cAAc,KAAK2D,KAAK,GAAG,eAAe,GAAG,EAAE,EAAG;cACtDM,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGtB,aAAa,CAACF,MAAM,CAACC,aAAa,CAAC;cAAI,CAAE;cAAAS,QAAA,GAE3DR,aAAa,CAACF,MAAM,CAACC,aAAa,CAAC,EAAC,GACvC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAtBEG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA,CAAC1D,IAAI,CAACkE,WAAW,iBAChBxE,OAAA;MAAK2D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5D,OAAA;QAAA4D,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BhE,OAAA;QAAA4D,QAAA,EAAG;MAAoG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CACN,eAEDhE,OAAA;MAAK2D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B5D,OAAA;QAAA4D,QAAA,GAAG,cAAY,EAACtD,IAAI,CAACqE,GAAG;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BhE,OAAA;QAAA4D,QAAA,GAAG,gBAAc,EAAC,EAAAxD,kBAAA,GAAAE,IAAI,CAACoB,YAAY,cAAAtB,kBAAA,uBAAjBA,kBAAA,CAAmBwE,MAAM,KAAI,CAAC;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9D,EAAA,CAxPQD,WAAW;EAAA,QACHL,SAAS;AAAA;AAAAiF,EAAA,GADjB5E,WAAW;AA0PpB,eAAeA,WAAW;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}