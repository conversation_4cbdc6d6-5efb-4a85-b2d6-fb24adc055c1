{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lpqa cpy\\\\frontend\\\\src\\\\components\\\\StatusDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport api from '../api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatusDisplay = () => {\n  _s();\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [polls, setPolls] = useState([]);\n  const [quizzes, setQuizzes] = useState([]);\n  useEffect(() => {\n    checkStatus();\n    const interval = setInterval(checkStatus, 5000); // Check every 5 seconds\n    return () => clearInterval(interval);\n  }, []);\n  const checkStatus = async () => {\n    try {\n      // Test backend health\n      await api.get('/health');\n      setBackendStatus('connected');\n\n      // Get current polls and quizzes\n      const [pollsResponse, quizzesResponse] = await Promise.all([api.get('/polls'), api.get('/quizzes')]);\n      setPolls(pollsResponse.data);\n      setQuizzes(quizzesResponse.data);\n    } catch (error) {\n      setBackendStatus('disconnected');\n    }\n  };\n  const getStatusColor = () => {\n    switch (backendStatus) {\n      case 'connected':\n        return '#4CAF50';\n      case 'disconnected':\n        return '#f44336';\n      default:\n        return '#ff9800';\n    }\n  };\n  const getStatusText = () => {\n    switch (backendStatus) {\n      case 'connected':\n        return '✅ Backend Connected';\n      case 'disconnected':\n        return '⚠️ Using Mock API';\n      default:\n        return '🔄 Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '10px',\n      right: '10px',\n      background: 'white',\n      border: `2px solid ${getStatusColor()}`,\n      borderRadius: '8px',\n      padding: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      zIndex: 1000,\n      minWidth: '200px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: getStatusColor(),\n        fontWeight: 'bold',\n        marginBottom: '5px'\n      },\n      children: getStatusText()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px',\n        color: '#666'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Polls: \", polls.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Quizzes: \", quizzes.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), polls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        fontSize: '11px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Available Poll Codes:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), polls.slice(0, 3).map(poll => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#e3f2fd',\n          padding: '2px 4px',\n          margin: '2px 0',\n          borderRadius: '3px'\n        },\n        children: [poll.sessionCode, \" - \", poll.question.substring(0, 20), \"...\"]\n      }, poll._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this), quizzes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        fontSize: '11px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Available Quiz Codes:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this), quizzes.slice(0, 3).map(quiz => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#fff3e0',\n          padding: '2px 4px',\n          margin: '2px 0',\n          borderRadius: '3px'\n        },\n        children: [quiz.sessionCode, \" - \", quiz.question.substring(0, 20), \"...\"]\n      }, quiz._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(StatusDisplay, \"Xy4QYZSC15JJnG1U4zVcxYxhIpM=\");\n_c = StatusDisplay;\nexport default StatusDisplay;\nvar _c;\n$RefreshReg$(_c, \"StatusDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "jsxDEV", "_jsxDEV", "StatusDisplay", "_s", "backendStatus", "setBackendStatus", "polls", "setPolls", "quizzes", "setQuizzes", "checkStatus", "interval", "setInterval", "clearInterval", "get", "pollsResponse", "quizzesResponse", "Promise", "all", "data", "error", "getStatusColor", "getStatusText", "style", "position", "top", "right", "background", "border", "borderRadius", "padding", "boxShadow", "zIndex", "min<PERSON><PERSON><PERSON>", "children", "color", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "length", "marginTop", "slice", "map", "poll", "margin", "sessionCode", "question", "substring", "_id", "quiz", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/components/StatusDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport api from '../api';\n\nconst StatusDisplay = () => {\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [polls, setPolls] = useState([]);\n  const [quizzes, setQuizzes] = useState([]);\n\n  useEffect(() => {\n    checkStatus();\n    const interval = setInterval(checkStatus, 5000); // Check every 5 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  const checkStatus = async () => {\n    try {\n      // Test backend health\n      await api.get('/health');\n      setBackendStatus('connected');\n      \n      // Get current polls and quizzes\n      const [pollsResponse, quizzesResponse] = await Promise.all([\n        api.get('/polls'),\n        api.get('/quizzes')\n      ]);\n      \n      setPolls(pollsResponse.data);\n      setQuizzes(quizzesResponse.data);\n    } catch (error) {\n      setBackendStatus('disconnected');\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (backendStatus) {\n      case 'connected': return '#4CAF50';\n      case 'disconnected': return '#f44336';\n      default: return '#ff9800';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (backendStatus) {\n      case 'connected': return '✅ Backend Connected';\n      case 'disconnected': return '⚠️ Using Mock API';\n      default: return '🔄 Checking...';\n    }\n  };\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '10px',\n      right: '10px',\n      background: 'white',\n      border: `2px solid ${getStatusColor()}`,\n      borderRadius: '8px',\n      padding: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      zIndex: 1000,\n      minWidth: '200px'\n    }}>\n      <div style={{ \n        color: getStatusColor(), \n        fontWeight: 'bold',\n        marginBottom: '5px'\n      }}>\n        {getStatusText()}\n      </div>\n      \n      <div style={{ fontSize: '12px', color: '#666' }}>\n        <div>Polls: {polls.length}</div>\n        <div>Quizzes: {quizzes.length}</div>\n      </div>\n\n      {polls.length > 0 && (\n        <div style={{ marginTop: '10px', fontSize: '11px' }}>\n          <strong>Available Poll Codes:</strong>\n          {polls.slice(0, 3).map(poll => (\n            <div key={poll._id} style={{ \n              background: '#e3f2fd', \n              padding: '2px 4px', \n              margin: '2px 0',\n              borderRadius: '3px'\n            }}>\n              {poll.sessionCode} - {poll.question.substring(0, 20)}...\n            </div>\n          ))}\n        </div>\n      )}\n\n      {quizzes.length > 0 && (\n        <div style={{ marginTop: '10px', fontSize: '11px' }}>\n          <strong>Available Quiz Codes:</strong>\n          {quizzes.slice(0, 3).map(quiz => (\n            <div key={quiz._id} style={{ \n              background: '#fff3e0', \n              padding: '2px 4px', \n              margin: '2px 0',\n              borderRadius: '3px'\n            }}>\n              {quiz.sessionCode} - {quiz.question.substring(0, 20)}...\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatusDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdY,WAAW,CAAC,CAAC;IACb,MAAMC,QAAQ,GAAGC,WAAW,CAACF,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;IACjD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF;MACA,MAAMX,GAAG,CAACe,GAAG,CAAC,SAAS,CAAC;MACxBT,gBAAgB,CAAC,WAAW,CAAC;;MAE7B;MACA,MAAM,CAACU,aAAa,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzDnB,GAAG,CAACe,GAAG,CAAC,QAAQ,CAAC,EACjBf,GAAG,CAACe,GAAG,CAAC,UAAU,CAAC,CACpB,CAAC;MAEFP,QAAQ,CAACQ,aAAa,CAACI,IAAI,CAAC;MAC5BV,UAAU,CAACO,eAAe,CAACG,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdf,gBAAgB,CAAC,cAAc,CAAC;IAClC;EACF,CAAC;EAED,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQjB,aAAa;MACnB,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQlB,aAAa;MACnB,KAAK,WAAW;QAAE,OAAO,qBAAqB;MAC9C,KAAK,cAAc;QAAE,OAAO,mBAAmB;MAC/C;QAAS,OAAO,gBAAgB;IAClC;EACF,CAAC;EAED,oBACEH,OAAA;IAAKsB,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE,OAAO;MACnBC,MAAM,EAAE,aAAaP,cAAc,CAAC,CAAC,EAAE;MACvCQ,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,4BAA4B;MACvCC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAjC,OAAA;MAAKsB,KAAK,EAAE;QACVY,KAAK,EAAEd,cAAc,CAAC,CAAC;QACvBe,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,EACCZ,aAAa,CAAC;IAAC;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAENxC,OAAA;MAAKsB,KAAK,EAAE;QAAEmB,QAAQ,EAAE,MAAM;QAAEP,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAC9CjC,OAAA;QAAAiC,QAAA,GAAK,SAAO,EAAC5B,KAAK,CAACqC,MAAM;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChCxC,OAAA;QAAAiC,QAAA,GAAK,WAAS,EAAC1B,OAAO,CAACmC,MAAM;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,EAELnC,KAAK,CAACqC,MAAM,GAAG,CAAC,iBACf1C,OAAA;MAAKsB,KAAK,EAAE;QAAEqB,SAAS,EAAE,MAAM;QAAEF,QAAQ,EAAE;MAAO,CAAE;MAAAR,QAAA,gBAClDjC,OAAA;QAAAiC,QAAA,EAAQ;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACrCnC,KAAK,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,iBACzB9C,OAAA;QAAoBsB,KAAK,EAAE;UACzBI,UAAU,EAAE,SAAS;UACrBG,OAAO,EAAE,SAAS;UAClBkB,MAAM,EAAE,OAAO;UACfnB,YAAY,EAAE;QAChB,CAAE;QAAAK,QAAA,GACCa,IAAI,CAACE,WAAW,EAAC,KAAG,EAACF,IAAI,CAACG,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACvD;MAAA,GAPUJ,IAAI,CAACK,GAAG;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOb,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEAjC,OAAO,CAACmC,MAAM,GAAG,CAAC,iBACjB1C,OAAA;MAAKsB,KAAK,EAAE;QAAEqB,SAAS,EAAE,MAAM;QAAEF,QAAQ,EAAE;MAAO,CAAE;MAAAR,QAAA,gBAClDjC,OAAA;QAAAiC,QAAA,EAAQ;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACrCjC,OAAO,CAACqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACO,IAAI,iBAC3BpD,OAAA;QAAoBsB,KAAK,EAAE;UACzBI,UAAU,EAAE,SAAS;UACrBG,OAAO,EAAE,SAAS;UAClBkB,MAAM,EAAE,OAAO;UACfnB,YAAY,EAAE;QAChB,CAAE;QAAAK,QAAA,GACCmB,IAAI,CAACJ,WAAW,EAAC,KAAG,EAACI,IAAI,CAACH,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACvD;MAAA,GAPUE,IAAI,CAACD,GAAG;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOb,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CAzGID,aAAa;AAAAoD,EAAA,GAAbpD,aAAa;AA2GnB,eAAeA,aAAa;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}