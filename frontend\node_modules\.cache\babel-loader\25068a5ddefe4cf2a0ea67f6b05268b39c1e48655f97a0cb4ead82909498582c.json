{"ast": null, "code": "import { io } from 'socket.io-client';\nconst socket = io('http://localhost:5000', {\n  autoConnect: true,\n  reconnection: true,\n  reconnectionDelay: 1000,\n  reconnectionAttempts: 5,\n  maxReconnectionAttempts: 5,\n  timeout: 5000\n});\n\n// Add connection event listeners for debugging\nsocket.on('connect', () => {\n  console.log('✅ Socket.IO connected successfully!', socket.id);\n});\nsocket.on('disconnect', reason => {\n  console.log('❌ Socket.IO disconnected:', reason);\n});\nsocket.on('connect_error', error => {\n  console.error('❌ Socket.IO connection error:', error);\n  console.error('❌ Make sure backend is running on http://localhost:5000');\n});\nsocket.on('error', error => {\n  console.error('❌ Socket.IO error:', error);\n});\n\n// Add vote-specific error handling\nsocket.on('vote-error', error => {\n  console.error('❌ Vote error:', error);\n  alert(`Vote failed: ${error.message || 'Unknown error'}`);\n});\nexport default socket;", "map": {"version": 3, "names": ["io", "socket", "autoConnect", "reconnection", "reconnectionDelay", "reconnectionAttempts", "maxReconnectionAttempts", "timeout", "on", "console", "log", "id", "reason", "error", "alert", "message"], "sources": ["C:/Users/<USER>/Downloads/lpqa cpy/frontend/src/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nconst socket = io('http://localhost:5000', {\n  autoConnect: true,\n  reconnection: true,\n  reconnectionDelay: 1000,\n  reconnectionAttempts: 5,\n  maxReconnectionAttempts: 5,\n  timeout: 5000\n});\n\n// Add connection event listeners for debugging\nsocket.on('connect', () => {\n  console.log('✅ Socket.IO connected successfully!', socket.id);\n});\n\nsocket.on('disconnect', (reason) => {\n  console.log('❌ Socket.IO disconnected:', reason);\n});\n\nsocket.on('connect_error', (error) => {\n  console.error('❌ Socket.IO connection error:', error);\n  console.error('❌ Make sure backend is running on http://localhost:5000');\n});\n\nsocket.on('error', (error) => {\n  console.error('❌ Socket.IO error:', error);\n});\n\n// Add vote-specific error handling\nsocket.on('vote-error', (error) => {\n  console.error('❌ Vote error:', error);\n  alert(`Vote failed: ${error.message || 'Unknown error'}`);\n});\n\nexport default socket;\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,kBAAkB;AAErC,MAAMC,MAAM,GAAGD,EAAE,CAAC,uBAAuB,EAAE;EACzCE,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,uBAAuB,EAAE,CAAC;EAC1BC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,MAAM,CAACO,EAAE,CAAC,SAAS,EAAE,MAAM;EACzBC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAET,MAAM,CAACU,EAAE,CAAC;AAC/D,CAAC,CAAC;AAEFV,MAAM,CAACO,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;EAClCH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,MAAM,CAAC;AAClD,CAAC,CAAC;AAEFX,MAAM,CAACO,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;EACpCJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;EACrDJ,OAAO,CAACI,KAAK,CAAC,yDAAyD,CAAC;AAC1E,CAAC,CAAC;AAEFZ,MAAM,CAACO,EAAE,CAAC,OAAO,EAAGK,KAAK,IAAK;EAC5BJ,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;AAC5C,CAAC,CAAC;;AAEF;AACAZ,MAAM,CAACO,EAAE,CAAC,YAAY,EAAGK,KAAK,IAAK;EACjCJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;EACrCC,KAAK,CAAC,gBAAgBD,KAAK,CAACE,OAAO,IAAI,eAAe,EAAE,CAAC;AAC3D,CAAC,CAAC;AAEF,eAAed,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}